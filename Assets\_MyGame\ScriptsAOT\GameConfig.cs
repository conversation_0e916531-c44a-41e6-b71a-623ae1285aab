using System;
using UnityEngine;

public partial class GameConfig
{
    public static bool UseOnlineProcess = true;

    /// <summary>
    /// 获取版本号
    /// </summary>
    public static string GetVer()
    {
        return VersionManager.GetVersion();
    }

    /// <summary>
    /// 获取版本代码
    /// </summary>
    public static int GetBuildVer()
    {
        return VersionManager.GetVersionCode();
    }

    /// <summary>
    /// 获取构建号
    /// </summary>
    public static string GetBuildNumber()
    {
        return VersionManager.GetBuildNumber();
    }

    private static string _cdnBase = "https://yrkp.feiyuapi.com/yrkp_online_cdn/";
    public static string CdnBase
    {
        get
        {
            if (!UseOnlineProcess)
            {
                return "http://192.168.77.66/bobo-tw-hot/";
            }

            // return "https://a.unity.cn/client_api/v1/buckets/751d8077-555c-434e-9dbf-2ea2ae9ed783/entry_by_path/content/?path=";
            return _cdnBase;
        }
    }
    private static string _cdnUrl;
    public static string CdnUrl
    {
        get
        {
            if (string.IsNullOrEmpty(_cdnUrl))
            {
#if UNITY_STANDALONE_WIN
                _cdnUrl = CdnBase + $"StandaloneWindows64/{GetVer()}";
#elif UNITY_ANDROID
                _cdnUrl = CdnBase + $"Android/{GetVer()}";
#elif UNITY_IOS
                _cdnUrl = CdnBase + $"iOS/{GetVer()}";
#elif UNITY_STANDALONE_OSX
                _cdnUrl = CdnBase + $"StandaloneOSX/{GetVer()}";
#elif UNITY_WEBGL
                _cdnUrl = CdnBase + $"WebGL/{GetVer()}";
                //_cdnUrl = CdnBase;
#endif
            }
            return _cdnUrl;
        }
    }

    public static void SetCDNBase(string value)
    {
        _cdnUrl = null;
        _cdnBase = value;

        _cdnBase = "http://192.168.77.66/bobo-tw-hot/";
    }
}