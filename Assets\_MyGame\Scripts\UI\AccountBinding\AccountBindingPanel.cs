using Common.NetMsg;
using FairyGUI;
using Google.Protobuf.Collections;
using Proto.LogicData;
using System;
using System.Linq;

public class AccountBindingPanel : Panel
{
    private GList listAddReward;
    private GButton BtnBinding;
    private GButton BtnGetReward;

    BaseNetActivityInfo activityInfo;

    public AccountBindingPanel()
    {
        packName = "AccountBinding";
        compName = "AccountBindingPanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        listAddReward = contentPane.GetChild("listAddReward").asList;
        listAddReward.itemRenderer = UpdateListReward;
        BtnBinding = contentPane.GetChild("BtnBinding").asButton;
        BtnGetReward = contentPane.GetChild("BtnGetReward").asButton;
        Request();

        Platform.GetInstance().GetBindData((bindList, unBindList) => {
            // 忽略大小写比较
            Session.needBindAccount = unBindList.Any(item =>
                 item.Equals("phone", StringComparison.OrdinalIgnoreCase));

            BtnBinding.visible = Session.needBindAccount;
            BtnGetReward.visible = !Session.needBindAccount;
        });
    }

    private void Request()
    {
        NetMgr.RequestActivitiesInfo((int)ActivityDisplayUiEnum.AccountBinding);
    }

    protected override void OnMessageRecieve(NetMessage msg)
    {
        var cmd = msg.GetCmd();
        switch (cmd)
        {
            case NetMsg.S2CRequestActivitiesInfoResult:
                var tempdataInfo = msg.Parse<RequestActivitiesInfoResult>();
                if (tempdataInfo.DisplayUi == (int)ActivityDisplayUiEnum.AccountBinding)
                {
                    if (tempdataInfo.ActivityList.Count > 0)
                    {
                        SetData(tempdataInfo.ActivityList);
                    }
                    else
                    {
                        Hide();
                    }
                }
                break;
            case NetMsg.S2CGetActivityRewardResult:
                var rewardResult = msg.Parse<RequestGetActivityRewardResult>();
                if (LangUtil.CheckResult(rewardResult.Result) && rewardResult.DisplayUi == (int)ActivityDisplayUiEnum.AccountBinding)
                {
                    Request();
                }
                break;
        }
    }

    private void SetData(RepeatedField<BaseNetActivityInfo> activityList)
    {
        if (activityList == null || activityList.Count == 0)
        {
            Hide();
            return;
        }
        activityInfo = activityList[0];
        listAddReward.data = activityInfo.RewardItemList;
        listAddReward.numItems = activityInfo.RewardItemList.Count;
    }

    private void UpdateListReward(int index, GObject obj)
    {
        var data = (listAddReward.data as RepeatedField<BaseNetItemInfo>)[index];
        var icon = obj.asCom.GetChild("item").asLoader;
        var bg = obj.asCom.GetChild("bg").asLoader;
        var lblItemCount = obj.asCom.GetChild("lblItemCount").asTextField;

        TipMgr.SetItemTip(icon, (int)data.ItemId);
        InfoItem info = ConfigItem.GetData((int)data.ItemId);
        if (icon != null)
        {
            icon.url = info.iconUrl;
        }
        if (bg != null)
        {
            bg.url = info.qualityUrl;
        }
        if (lblItemCount != null)
        {
            lblItemCount.text = MathUtil.ConvertNumWithUnit(data.Count);
        }
    }

    private void OnBinding()
    {
        Platform.GetInstance().ShowBindCenter(new string[] { "phone"}, (type, stutas) =>
        {
            if (stutas == "success" || stutas == "already")
            {
                NetMgr.GetActivityRewardByCache(activityInfo.Id);
            }

        });
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "BtnClose":
                Hide();
                break;
            case "btnRefuse":
                Hide();
                break;
            case "BtnBinding":
                OnBinding();
                break;
            case "BtnGetReward":
                if(activityInfo != null)
                {
                    NetMgr.GetActivityRewardByCache(activityInfo.Id);
                }
                break;
        }
    }
}