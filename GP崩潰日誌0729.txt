1.
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
pid: 0, tid: 15496 >>> com.touchbox.sdz <<<

backtrace:
  #00  pc 0x00000000007c48f4  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #01  pc 0x00000000007b78c8  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #02  pc 0x00000000007b65f8  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #03  pc 0x00000000007b614c  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #04  pc 0x00000000007b4810  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #05  pc 0x00000000007b3008  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #06  pc 0x00000000007b2a98  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #07  pc 0x0000000000326b20  /data/app/com.touchbox.sdz-9z7q2EWgLayNpnO13YizjA==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #08  pc 0x0000000000091fac  /system/lib64/libc.so (__pthread_start(void*)+36)
  #09  pc 0x0000000000023968  /system/lib64/libc.so (__start_thread+68)


2.
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
pid: 0, tid: 21782 >>> com.touchbox.sdz <<<

backtrace:
  #00  pc 0x000000000023a3d4  /vendor/lib64/hw/vulkan.adreno.so
  #01  pc 0x00000000000fb5e0  /vendor/lib64/hw/vulkan.adreno.so


3.
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
pid: 0, tid: 6185 >>> com.touchbox.sdz <<<

backtrace:
  #00  pc 0x00000000000938b8  /apex/com.android.runtime/lib64/bionic/libc.so (abort+164)
  #01  pc 0x00000000008a14a4  /apex/com.android.art/lib64/libart.so (art::Runtime::Abort(char const*)+476)
  #02  pc 0x0000000000016188  /apex/com.android.art/lib64/libbase.so (android::base::SetAborter(std::__1::function<void (char const*)>&&)::$_0::__invoke(char const*)+80)
  #03  pc 0x0000000000015730  /apex/com.android.art/lib64/libbase.so (android::base::LogMessage::~LogMessage()+544)
  #04  pc 0x0000000000585a5c  /apex/com.android.art/lib64/libart.so (art::JavaVMExt::AddGlobalRef(art::Thread*, art::ObjPtr<art::mirror::Object>)+1440)
  #05  pc 0x0000000000588514  /apex/com.android.art/lib64/libart.so (art::JNI<false>::NewGlobalRef(_JNIEnv*, _jobject*)+164)
  #06  pc 0x00000000071da6c4  /data/app/~~WAEzImYUTqvyAEvEj2fwPQ==/com.google.android.trichromelibrary_720415733-C-yZnBk7IWn4PsHQMlmLIQ==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #07  pc 0x000000000404d4cc  /data/app/~~WAEzImYUTqvyAEvEj2fwPQ==/com.google.android.trichromelibrary_720415733-C-yZnBk7IWn4PsHQMlmLIQ==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #08  pc 0x000000000404d280  /data/app/~~WAEzImYUTqvyAEvEj2fwPQ==/com.google.android.trichromelibrary_720415733-C-yZnBk7IWn4PsHQMlmLIQ==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #09  pc 0x00000000062f48e8  /data/app/~~WAEzImYUTqvyAEvEj2fwPQ==/com.google.android.trichromelibrary_720415733-C-yZnBk7IWn4PsHQMlmLIQ==/base.apk!libmonochrome_64.so (Java_J_N_VOZ+612) (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #10  pc 0x0000000000dd0ac0  /data/misc/apexdata/com.android.art/dalvik-cache/arm64/boot.oat (art_jni_trampoline+128)
  #11  pc 0x0000000000689588  /apex/com.android.art/lib64/libart.so (nterp_helper+152)
  #12  pc 0x00000000001cf460  /data/app/~~6TUzap58LU2vBezvoKU_cA==/com.google.android.webview-jHdeAPH3iHt03UEZJWO5Rw==/base.apk (org.chromium.base.JavaExceptionReporter.uncaughtException+48)
  #13  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #14  pc 0x000000000045d92e  /data/app/~~K2YKfRBCUi9COa5suligQQ==/com.touchbox.sdz-FrIw4YBCxSiFoggpyzSvZQ==/base.apk (com.gd.gdinfo.crash.GDCrashHandler.uncaughtException+210)
  #15  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #16  pc 0x00000000002af350  /data/app/~~K2YKfRBCUi9COa5suligQQ==/com.touchbox.sdz-FrIw4YBCxSiFoggpyzSvZQ==/base.apk (com.ironsource.adqualitysdk.sdk.i.ai$2.uncaughtException+216)
  #17  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #18  pc 0x00000000004bec8c  /data/app/~~K2YKfRBCUi9COa5suligQQ==/com.touchbox.sdz-FrIw4YBCxSiFoggpyzSvZQ==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.a+16)
  #19  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #20  pc 0x00000000004bee2c  /data/app/~~K2YKfRBCUi9COa5suligQQ==/com.touchbox.sdz-FrIw4YBCxSiFoggpyzSvZQ==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.b+332)
  #21  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #22  pc 0x00000000004bee84  /data/app/~~K2YKfRBCUi9COa5suligQQ==/com.touchbox.sdz-FrIw4YBCxSiFoggpyzSvZQ==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.uncaughtException+4)
  #23  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #24  pc 0x0000000000118b70  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+32)
  #25  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #26  pc 0x0000000000118b5c  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+12)
  #27  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #28  pc 0x0000000000119e9e  /apex/com.android.art/javalib/core-oj.jar (java.lang.Thread.dispatchUncaughtException+30)
  #29  pc 0x0000000000328194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612)
  #30  pc 0x00000000002d9348  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+216)
  #31  pc 0x00000000005989e8  /apex/com.android.art/lib64/libart.so (art::detail::ShortyTraits<(char)86>::Type art::ArtMethod::InvokeInstance<(char)86, (char)76>(art::Thread*, art::ObjPtr<art::mirror::Object>, art::detail::ShortyTraits<(char)76>::Type)+68)
  #32  pc 0x0000000000426dc0  /apex/com.android.art/lib64/libart.so (art::Thread::Destroy(bool)+1440)
  #33  pc 0x0000000000425498  /apex/com.android.art/lib64/libart.so (art::ThreadList::Unregister(art::Thread*, bool)+152)
  #34  pc 0x00000000004252fc  /apex/com.android.art/lib64/libart.so (art::Runtime::DetachCurrentThread(bool)+112)
  #35  pc 0x0000000000427834  /apex/com.android.art/lib64/libart.so (art::JII::DetachCurrentThread(_JavaVM*)+52)
  #36  pc 0x00000000000edfdc  /system/lib64/libandroid_runtime.so (android::AndroidRuntime::start(char const*, android::Vector<android::String8> const&, bool)+984)
  #37  pc 0x0000000000010550  /system/bin/app_process64 (main+1276)
  #38  pc 0x000000000008c400  /apex/com.android.runtime/lib64/bionic/libc.so (__libc_init+104)

4.
Exception Revision: '0'
ABI: 'arm64'
Timestamp: 2025-07-24 13:55:53+0800
pid: 26518, tid: 26896, name: Vulkan Submissi  >>> com.touchbox.sdz <<<
uid: 10286
signal 11 (SIGSEGV), code 1 (SEGV_MAPERR), fault addr 0x8
Cause: null pointer dereference
    x0  0000007620f33250  x1  b40000787e262850  x2  00000075f01231e8  x3  0000000000000007
    x4  0000000000001000  x5  0000000000001000  x6  00000076cfc458a0  x7  0000000000000000
    x8  0000000000000001  x9  0000000000001000  x10 00000000ffffffff  x11 0000007620f348f0
    x12 0000000000000018  x13 0000000000000000  x14 00000075f01234f0  x15 00000000ac3b7fa9
    x16 000000771949fac8  x17 0000007a816891c0  x18 00000075d040a000  x19 0000000000000000
    x20 0000000000000000  x21 0000000000000000  x22 0000000000000000  x23 0000000000000000
    x24 0000000000000000  x25 0000000000000000  x26 0000000000000328  x27 00000076cfc458a0
    x28 0000000000001000  x29 0000000000000007
    sp  00000076cfc45730  lr  0000007718a3f8cc  pc  0000007718a4c8f4

backtrace:
      #00 pc 00000000007c48f4  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #01 pc 00000000007b78c8  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #02 pc 00000000007b65f8  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #03 pc 00000000007b614c  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #04 pc 00000000007b4810  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #05 pc 00000000007b3008  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #06 pc 00000000007b2a98  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #07 pc 0000000000326b20  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #08 pc 00000000000c1b40  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+224) (BuildId: 65f0a078b7cc159a5d3df081e44378ff)
      #09 pc 0000000000054f20  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 65f0a078b7cc159a5d3df081e44378ff)

-2
java.lang.Error: FATAL EXCEPTION [main]
Unity version     : 2021.3.7f1
Device model      : vivo V2124
Device fingerprint: vivo/2124N/2124:13/TP1A.220624.014/compiler10182342:user/release-keys
Build Type        : Release
Scripting Backend : IL2CPP
ABI               : arm64-v8a
Strip Engine Code : true

Caused by: java.lang.Error: *** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
Version '2021.3.7f1 (24e8595d6d43)', Build type 'Release', Scripting Backend 'il2cpp', CPU 'arm64-v8a'
Build fingerprint: 'vivo/2124N/2124:13/TP1A.220624.014/compiler10182342:user/release-keys'
Revision: '0'
ABI: 'arm64'
Timestamp: 2025-07-24 13:55:53+0800
pid: 26518, tid: 26896, name: Vulkan Submissi  >>> com.touchbox.sdz <<<
uid: 10286
signal 11 (SIGSEGV), code 1 (SEGV_MAPERR), fault addr 0x8
Cause: null pointer dereference
    x0  0000007620f33250  x1  b40000787e262850  x2  00000075f01231e8  x3  0000000000000007
    x4  0000000000001000  x5  0000000000001000  x6  00000076cfc458a0  x7  0000000000000000
    x8  0000000000000001  x9  0000000000001000  x10 00000000ffffffff  x11 0000007620f348f0
    x12 0000000000000018  x13 0000000000000000  x14 00000075f01234f0  x15 00000000ac3b7fa9
    x16 000000771949fac8  x17 0000007a816891c0  x18 00000075d040a000  x19 0000000000000000
    x20 0000000000000000  x21 0000000000000000  x22 0000000000000000  x23 0000000000000000
    x24 0000000000000000  x25 0000000000000000  x26 0000000000000328  x27 00000076cfc458a0
    x28 0000000000001000  x29 0000000000000007
    sp  00000076cfc45730  lr  0000007718a3f8cc  pc  0000007718a4c8f4

backtrace:
      #00 pc 00000000007c48f4  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #01 pc 00000000007b78c8  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #02 pc 00000000007b65f8  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #03 pc 00000000007b614c  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #04 pc 00000000007b4810  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #05 pc 00000000007b3008  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #06 pc 00000000007b2a98  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #07 pc 0000000000326b20  /data/app/~~k-DHAWAIuOW7yfQdOkoOOQ==/com.touchbox.sdz-0xGphqWlqk6JDEkTp5LWmg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
      #08 pc 00000000000c1b40  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+224) (BuildId: 65f0a078b7cc159a5d3df081e44378ff)
      #09 pc 0000000000054f20  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: 65f0a078b7cc159a5d3df081e44378ff)
  at libunity
  at libunity
  at libunity
  at libunity
  at libunity
  at libunity
  at libunity
  at libunity
  at libc.__pthread_start(void*) (__pthread_start:224)
  at libc.__start_thread (__start_thread:64)


5.
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
pid: 0, tid: 21384 >>> com.touchbox.sdz <<<

backtrace:
  #00  pc 0x0000000000052654  /apex/com.android.runtime/lib64/bionic/libc.so (abort+168)
  #01  pc 0x00000000008a14a4  /apex/com.android.art/lib64/libart.so (art::Runtime::Abort(char const*)+476)
  #02  pc 0x0000000000016188  /apex/com.android.art/lib64/libbase.so (android::base::SetAborter(std::__1::function<void (char const*)>&&)::$_0::__invoke(char const*)+80)
  #03  pc 0x0000000000015730  /apex/com.android.art/lib64/libbase.so (android::base::LogMessage::~LogMessage()+544)
  #04  pc 0x0000000000585a5c  /apex/com.android.art/lib64/libart.so (art::JavaVMExt::AddGlobalRef(art::Thread*, art::ObjPtr<art::mirror::Object>)+1440)
  #05  pc 0x0000000000588514  /apex/com.android.art/lib64/libart.so (art::JNI<false>::NewGlobalRef(_JNIEnv*, _jobject*)+164)
  #06  pc 0x00000000071da6c4  /data/app/~~EM0WC5yQf2WKIUZRgginpg==/com.google.android.trichromelibrary_720415733-gpE-1lDJCHbau-6Wflb-dw==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #07  pc 0x000000000404d4cc  /data/app/~~EM0WC5yQf2WKIUZRgginpg==/com.google.android.trichromelibrary_720415733-gpE-1lDJCHbau-6Wflb-dw==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #08  pc 0x000000000404d280  /data/app/~~EM0WC5yQf2WKIUZRgginpg==/com.google.android.trichromelibrary_720415733-gpE-1lDJCHbau-6Wflb-dw==/base.apk!libmonochrome_64.so (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #09  pc 0x00000000062f48e8  /data/app/~~EM0WC5yQf2WKIUZRgginpg==/com.google.android.trichromelibrary_720415733-gpE-1lDJCHbau-6Wflb-dw==/base.apk!libmonochrome_64.so (Java_J_N_VOZ+612) (BuildId: 627a8094a7ad94adeeb06a1fce2ac1fc36d688ee)
  #10  pc 0x0000000000e525a4  /data/misc/apexdata/com.android.art/dalvik-cache/arm64/boot.oat (art_jni_trampoline+132)
  #11  pc 0x0000000000689588  /apex/com.android.art/lib64/libart.so (nterp_helper+152)
  #12  pc 0x00000000001cf460  /data/app/~~ccjoSZCbhBMhrCxU0uJspg==/com.google.android.webview-2HuivoFgHST4FOhl-Nw3Bg==/base.apk (org.chromium.base.JavaExceptionReporter.uncaughtException+48)
  #13  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #14  pc 0x000000000045d92e  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.gd.gdinfo.crash.GDCrashHandler.uncaughtException+210)
  #15  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #16  pc 0x00000000002af350  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.ironsource.adqualitysdk.sdk.i.ai$2.uncaughtException+216)
  #17  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #18  pc 0x00000000004bec8c  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.a+16)
  #19  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #20  pc 0x00000000004bee2c  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.b+332)
  #21  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #22  pc 0x00000000004bee84  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.mbridge.msdk.foundation.same.report.crashreport.e.uncaughtException+4)
  #23  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #24  pc 0x00000000003bb55a  /data/app/~~RPqHVzMzLfChXM_0nqmhtg==/com.touchbox.sdz-k_7Gi1HGs6LsLocSY8eaRg==/base.apk (com.ironsource.l9.uncaughtException+90)
  #25  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #26  pc 0x0000000000118b70  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+32)
  #27  pc 0x000000000068a444  /apex/com.android.art/lib64/libart.so (nterp_helper+3924)
  #28  pc 0x0000000000118b5c  /apex/com.android.art/javalib/core-oj.jar (java.lang.ThreadGroup.uncaughtException+12)
  #29  pc 0x000000000068b264  /apex/com.android.art/lib64/libart.so (nterp_helper+7540)
  #30  pc 0x0000000000119e9e  /apex/com.android.art/javalib/core-oj.jar (java.lang.Thread.dispatchUncaughtException+30)
  #31  pc 0x0000000000328194  /apex/com.android.art/lib64/libart.so (art_quick_invoke_stub+612)
  #32  pc 0x00000000002d9348  /apex/com.android.art/lib64/libart.so (art::ArtMethod::Invoke(art::Thread*, unsigned int*, unsigned int, art::JValue*, char const*)+216)
  #33  pc 0x00000000005989e8  /apex/com.android.art/lib64/libart.so (art::detail::ShortyTraits<(char)86>::Type art::ArtMethod::InvokeInstance<(char)86, (char)76>(art::Thread*, art::ObjPtr<art::mirror::Object>, art::detail::ShortyTraits<(char)76>::Type)+68)
  #34  pc 0x0000000000426dc0  /apex/com.android.art/lib64/libart.so (art::Thread::Destroy(bool)+1440)
  #35  pc 0x0000000000425498  /apex/com.android.art/lib64/libart.so (art::ThreadList::Unregister(art::Thread*, bool)+152)
  #36  pc 0x00000000004252fc  /apex/com.android.art/lib64/libart.so (art::Runtime::DetachCurrentThread(bool)+112)
  #37  pc 0x0000000000427834  /apex/com.android.art/lib64/libart.so (art::JII::DetachCurrentThread(_JavaVM*)+52)
  #38  pc 0x00000000000ce4f4  /system/lib64/libandroid_runtime.so (android::AndroidRuntime::start(char const*, android::Vector<android::String8> const&, bool)+988)
  #39  pc 0x0000000000002570  /system/bin/app_process64 (main+1304)
  #40  pc 0x000000000004a7d4  /apex/com.android.runtime/lib64/bionic/libc.so (__libc_init+100)

6.
*** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
pid: 0, tid: 907 >>> com.touchbox.sdz <<<

backtrace:
  #00  pc 0x000000000032685c  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #01  pc 0x000000000027d014  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #02  pc 0x000000000027a604  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #03  pc 0x00000000007189b8  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #04  pc 0x00000000008837dc  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #05  pc 0x0000000000884be0  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #06  pc 0x000000000087c470  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #07  pc 0x0000000000326b20  /data/app/~~g8k-kp6W45tn1B9o4bnDxw==/com.touchbox.sdz-ZqBzGPdg04uCHOQXJmtQpg==/lib/arm64/libunity.so (BuildId: 10f32e50e1acd23994e1fed9556760473ee2e921)
  #08  pc 0x00000000000da278  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+64)
  #09  pc 0x000000000007a448  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64)