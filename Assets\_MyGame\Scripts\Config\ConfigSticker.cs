using System.Collections.Generic;
using DashGame;
using UnityEngine;

public class ConfigSticker : ConfigManager
{
    public static DictList<int, List<InfoSticker>> GetAllList()
    {
        var stickerList = ConfigHelper.GetManager<ConfigSticker>().GetDataList<InfoSticker>();
        var result = new DictList<int, List<InfoSticker>>();
        foreach (var emoji in stickerList)
        {
            if (!result.ContainsKey(emoji.groupID))
            {
                result.Add(emoji.groupID, new List<InfoSticker>());
            }
            result.GetValueByKey(emoji.groupID).Add(emoji);
        }
        return result;
    }

    public static List<InfoSticker> GetListByGroup(int groupID)
    {
        var stickerDict = GetAllList();

        if (!stickerDict.ContainsKey(groupID))
        {
            Debug.LogError($"Group {groupID} not found.");
            return null;
        }

        return stickerDict.GetValueBy<PERSON>ey(groupID);
    }

    public static string GetEffectIDByName(string name)
    {
        var stickerList = ConfigHelper.GetManager<ConfigSticker>().GetDataList<InfoSticker>();
        foreach (var emoji in stickerList)
        {
            if (emoji.name == name)
            {
                return emoji.effectID;
            }
        }
        return null;
    }
}