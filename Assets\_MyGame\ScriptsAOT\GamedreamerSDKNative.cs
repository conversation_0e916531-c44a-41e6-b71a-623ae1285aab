using System;
using System.Runtime.InteropServices;

/// <summary>
/// GamedreamerSDK 原生方法包装类 - 放在 AOT 程序集中
/// 用于解决 iOS 平台不支持热更新程序集中 PInvoke 的问题
/// </summary>
public static class GamedreamerSDKNative
{
#if UNITY_IOS 
    #region iOS原生方法声明
    [DllImport("__Internal")]
    private static extern void _GamedreamerLogin();

    [DllImport("__Internal")]
    private static extern void _GamedreamerCheckServer(IntPtr serverId);

    [DllImport("__Internal")]
    private static extern bool _GamedreamerLogout();

    [DllImport("__Internal")]
    private static extern bool _GamedreamerNewRole(IntPtr roleName, IntPtr roleId);

    [DllImport("__Internal")]
    private static extern bool _GamedreamerSaveRole(IntPtr roleName, IntPtr roleId, IntPtr roleLevel);

    [DllImport("__Internal")]
    private static extern void _GamedreamerShowMemberCenter();

    [DllImport("__Internal")]
    private static extern bool _GamedreamerStartGameForEventRecorded();

    [DllImport("__Internal")]
    private static extern void _GamedreamerStoreWithProItemid(IntPtr proItemId, IntPtr validation);

    [DllImport("__Internal")]
    private static extern void _GamedreamerShowCustomerService();

    [DllImport("__Internal")]
    private static extern void _GamedreamerGetGoodsLocalInfo();

    [DllImport("__Internal")]
    private static extern void _GamedreamerAppScore();

    [DllImport("__Internal")]
    private static extern void _GamedreamerVibrate(int type);

    [DllImport("__Internal")]
    private static extern void _GamedreamerCopyToClipboard(IntPtr text);

    [DllImport("__Internal")]
    private static extern void _GamedreamerGenerateInviteLink(IntPtr inviteEventKey, IntPtr inviteEventValue, int inviteChannel);

    [DllImport("__Internal")]
    private static extern void _GamedreamerCheckInvitationRewards();

    [DllImport("__Internal")]
    private static extern void _GamedreamerSystemShare(IntPtr title, IntPtr link);

    [DllImport("__Internal")]
    private static extern void _GamedreamerSystemShareImage(IntPtr title, IntPtr imageBase64);

    [DllImport("__Internal")]
    private static extern void _GamedreamerLogEventWithName(IntPtr eventName, IntPtr parametersJson);

    [DllImport("__Internal")]
    private static extern void _GamedreamerJumpAppStore();

    [DllImport("__Internal")]
    private static extern void _GamedreamerBind(IntPtr bindListJson);

    [DllImport("__Internal")]
    private static extern void _GamedreamerGetBindData();
    #endregion

    #region 公共包装方法
    public static void Login()
    {
        _GamedreamerLogin();
    }

    public static void CheckServer(string serverId)
    {
        IntPtr serverIdPtr = Marshal.StringToHGlobalAnsi(serverId);
        try
        {
            _GamedreamerCheckServer(serverIdPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(serverIdPtr);
        }
    }

    public static bool Logout()
    {
        return _GamedreamerLogout();
    }

    public static bool NewRole(string roleName, string roleId)
    {
        IntPtr roleNamePtr = Marshal.StringToHGlobalAnsi(roleName);
        IntPtr roleIdPtr = Marshal.StringToHGlobalAnsi(roleId);
        try
        {
            return _GamedreamerNewRole(roleNamePtr, roleIdPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(roleNamePtr);
            Marshal.FreeHGlobal(roleIdPtr);
        }
    }

    public static bool SaveRole(string roleName, string roleId, string roleLevel)
    {
        IntPtr roleNamePtr = Marshal.StringToHGlobalAnsi(roleName);
        IntPtr roleIdPtr = Marshal.StringToHGlobalAnsi(roleId);
        IntPtr roleLevelPtr = Marshal.StringToHGlobalAnsi(roleLevel);
        try
        {
            return _GamedreamerSaveRole(roleNamePtr, roleIdPtr, roleLevelPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(roleNamePtr);
            Marshal.FreeHGlobal(roleIdPtr);
            Marshal.FreeHGlobal(roleLevelPtr);
        }
    }

    public static void ShowMemberCenter()
    {
        _GamedreamerShowMemberCenter();
    }

    public static bool StartGameForEventRecorded()
    {
        return _GamedreamerStartGameForEventRecorded();
    }

    public static void StoreWithProItemid(string proItemId, string validation)
    {
        IntPtr proItemIdPtr = Marshal.StringToHGlobalAnsi(proItemId);
        IntPtr validationPtr = Marshal.StringToHGlobalAnsi(validation);
        try
        {
            _GamedreamerStoreWithProItemid(proItemIdPtr, validationPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(proItemIdPtr);
            Marshal.FreeHGlobal(validationPtr);
        }
    }

    public static void ShowCustomerService()
    {
        _GamedreamerShowCustomerService();
    }

    public static void GetGoodsLocalInfo()
    {
        _GamedreamerGetGoodsLocalInfo();
    }

    public static void AppScore()
    {
        _GamedreamerAppScore();
    }

    public static void Vibrate(int type)
    {
        _GamedreamerVibrate(type);
    }

    public static void CopyToClipboard(string text)
    {
        IntPtr textPtr = Marshal.StringToHGlobalAnsi(text);
        try
        {
            _GamedreamerCopyToClipboard(textPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(textPtr);
        }
    }

    public static void GenerateInviteLink(string inviteEventKey, string inviteEventValue, int inviteChannel)
    {
        IntPtr inviteEventKeyPtr = Marshal.StringToHGlobalAnsi(inviteEventKey);
        IntPtr inviteEventValuePtr = Marshal.StringToHGlobalAnsi(inviteEventValue);
        try
        {
            _GamedreamerGenerateInviteLink(inviteEventKeyPtr, inviteEventValuePtr, inviteChannel);
        }
        finally
        {
            Marshal.FreeHGlobal(inviteEventKeyPtr);
            Marshal.FreeHGlobal(inviteEventValuePtr);
        }
    }

    public static void CheckInvitationRewards()
    {
        _GamedreamerCheckInvitationRewards();
    }

    public static void SystemShare(string title, string link)
    {
        IntPtr titlePtr = Marshal.StringToHGlobalAnsi(title);
        IntPtr linkPtr = Marshal.StringToHGlobalAnsi(link);
        try
        {
            _GamedreamerSystemShare(titlePtr, linkPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(titlePtr);
            Marshal.FreeHGlobal(linkPtr);
        }
    }

    public static void SystemShareImage(string title, string imageBase64)
    {
        IntPtr titlePtr = Marshal.StringToHGlobalAnsi(title);
        IntPtr imageBase64Ptr = Marshal.StringToHGlobalAnsi(imageBase64);
        try
        {
            _GamedreamerSystemShareImage(titlePtr, imageBase64Ptr);
        }
        finally
        {
            Marshal.FreeHGlobal(titlePtr);
            Marshal.FreeHGlobal(imageBase64Ptr);
        }
    }

    public static void LogEventWithName(string eventName, string parametersJson)
    {
        IntPtr eventNamePtr = Marshal.StringToHGlobalAnsi(eventName);
        IntPtr parametersJsonPtr = Marshal.StringToHGlobalAnsi(parametersJson);
        try
        {
            _GamedreamerLogEventWithName(eventNamePtr, parametersJsonPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(eventNamePtr);
            Marshal.FreeHGlobal(parametersJsonPtr);
        }
    }

    public static void JumpAppStore()
    {
        _GamedreamerJumpAppStore();
    }

    public static void GamedreamerBind(string[] bindList)
    {
        // 将字符串数组转换为JSON格式
        string bindListJson = "[\"" + string.Join("\",\"", bindList) + "\"]";
        IntPtr bindListJsonPtr = Marshal.StringToHGlobalAnsi(bindListJson);
        try
        {
            _GamedreamerBind(bindListJsonPtr);
        }
        finally
        {
            Marshal.FreeHGlobal(bindListJsonPtr);
        }
    }

    public static void GamedreamerGetBindData()
    {
        _GamedreamerGetBindData();
    }
    #endregion

#endif
}
