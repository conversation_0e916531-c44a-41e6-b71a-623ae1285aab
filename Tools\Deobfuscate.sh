#!/bin/bash

# 设置脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TOOL_PATH="$SCRIPT_DIR/DeobfuscateStackTrace/net8.0/DeobfuscateStackTrace.dll"
MAPPING_FILE="$SCRIPT_DIR/../Assets/Obfuz/SymbolObfus/symbol-mapping.xml"

# 输入和输出文件
INPUT_FILE="$SCRIPT_DIR/obfuscate.txt"
OUTPUT_FILE="/tmp/deobfuscate.txt"

# 检查 dotnet 是否安装
if ! command -v dotnet &> /dev/null; then
    echo "错误: 未找到 dotnet 命令。请确保已安装 .NET Runtime。"
    exit 1
fi

# 检查工具文件是否存在
if [ ! -f "$TOOL_PATH" ]; then
    echo "错误: 未找到反混淆工具: $TOOL_PATH"
    exit 1
fi

# 检查映射文件是否存在
if [ ! -f "$MAPPING_FILE" ]; then
    echo "错误: 未找到符号映射文件: $MAPPING_FILE"
    exit 1
fi

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "错误: 未找到输入文件: $INPUT_FILE"
    exit 1
fi

# 执行反混淆
echo "正在执行反混淆..."
# 尝试使用可用的 .NET 版本运行，首先尝试 9.0.5，然后尝试 6.0.36
if dotnet exec --fx-version 9.0.5 "$TOOL_PATH" -r -m "$MAPPING_FILE" -i "$INPUT_FILE" -o "$OUTPUT_FILE" 2>/dev/null; then
    echo "使用 .NET 9.0.5 执行成功"
else
    echo "尝试使用默认 .NET 版本..."
    dotnet "$TOOL_PATH" -r -m "$MAPPING_FILE" -i "$INPUT_FILE" -o "$OUTPUT_FILE"
fi

# 检查执行结果
if [ $? -eq 0 ]; then
    echo
    echo "========== 反混淆结果 =========="
    echo
    cat "$OUTPUT_FILE"
    echo
    echo "==============================="
    echo
    echo "反混淆完成！结果已保存到: $OUTPUT_FILE"
else
    echo "反混淆执行失败！"
    exit 1
fi

# 等待用户按键（可选）
read -p "按回车键继续..."
