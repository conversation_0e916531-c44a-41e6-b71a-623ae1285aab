using System;
using UnityEngine;

/// <summary>
/// 版本信息数据结构
/// </summary>
[Serializable]
public class VersionData
{
    public string version = "1.0.0";
    public int versionCode = 1;
    public string buildNumber = "";
    public string lastUpdateTime = "";
    public string platformId = "google";
}

/// <summary>
/// 版本管理器 - 从Resources/version.json读取版本信息
/// </summary>
public static class VersionManager
{
    private static VersionData _cachedVersionData;
    private static bool _isLoaded = false;

    /// <summary>
    /// 获取版本数据
    /// </summary>
    public static VersionData GetVersionData()
    {
        if (!_isLoaded)
        {
            LoadVersionData();
        }
        return _cachedVersionData;
    }

    /// <summary>
    /// 获取版本号
    /// </summary>
    public static string GetVersion()
    {
        return GetVersionData().version;
    }

    /// <summary>
    /// 获取版本代码
    /// </summary>
    public static int GetVersionCode()
    {
        return GetVersionData().versionCode;
    }

    /// <summary>
    /// 获取构建号
    /// </summary>
    public static string GetBuildNumber()
    {
        return GetVersionData().buildNumber;
    }

    /// <summary>
    /// 获取最后更新时间
    /// </summary>
    public static string GetLastUpdateTime()
    {
        return GetVersionData().lastUpdateTime;
    }

    /// <summary>
    /// 获取平台ID
    /// </summary>
    /// <returns></returns>
    public static string GetPlatformId()
    {
#if UNITY_IOS//ios写死
        return "apple";
#else
        return GetVersionData().platformId;
#endif
    }

    /// <summary>
    /// 重新加载版本数据
    /// </summary>
    public static void ReloadVersionData()
    {
        _isLoaded = false;
        LoadVersionData();
    }

    /// <summary>
    /// 加载版本数据
    /// </summary>
    private static void LoadVersionData()
    {
        try
        {
            TextAsset versionFile = Resources.Load<TextAsset>("version");
            if (versionFile != null)
            {
                _cachedVersionData = JsonUtility.FromJson<VersionData>(versionFile.text);
                _isLoaded = true;
                Debug.Log($"✅ [VersionManager] 版本: v{_cachedVersionData.version}({_cachedVersionData.versionCode})");
            }
            else
            {
                Debug.LogError("❌ [VersionManager] 版本文件不存在: Resources/version.json");
                CreateDefaultVersionData();
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"❌ [VersionManager] 加载版本数据失败: {e.Message}");
            CreateDefaultVersionData();
        }
    }

    /// <summary>
    /// 创建默认版本数据
    /// </summary>
    private static void CreateDefaultVersionData()
    {
        _cachedVersionData = new VersionData
        {
            version = "1.0.0",
            versionCode = 1,
            buildNumber = DateTime.Now.ToString("yyyyMMddHHmmss"),
            lastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        };
        _isLoaded = true;
    }
}
