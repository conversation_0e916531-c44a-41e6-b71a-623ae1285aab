using FairyGUI;
using Google.Protobuf.Collections;
using Proto.LogicData;
using System.Collections.Generic;
using System.Linq;

public class SeasonFundBuyPreview : Panel
{
    public SeasonFundBuyPreview()
    {
        packName = "SeasonPasses";
        compName = "FundBuyPreview";
        modal = true;
    }

    private GButton btnBuyHigh;
    private GList listNowReward;
    private GList listTotalReward;
    private GTextField lblNotReward;

    List<BaseNetItemInfo> totalRewards;
    List<BaseNetItemInfo> canGetRewards;
    protected override void DoInitialize()
    {
        btnBuyHigh = contentPane.GetChild("btnBuyHigh").asButton;
        listNowReward = contentPane.GetChild("listNowReward").asList;
        listTotalReward = contentPane.GetChild("listTotalReward").asList;
        lblNotReward = contentPane.GetChild("lblNotReward").asTextField;
        listNowReward.itemRenderer = UpdateNowItem;
        listTotalReward.itemRenderer = UpdateTotalItem;
    }

    private void UpdateNowItem(int index, GObject item)
    {
        UpdateItem(index, item, canGetRewards);
    }

    private void UpdateTotalItem(int index, GObject item)
    {
        UpdateItem(index, item, totalRewards);
    }

    private void UpdateItem(int index, GObject item, List<BaseNetItemInfo> rewards)
    {
        var bg = item.asCom.GetChild("bg").asLoader;
        var icon = item.asCom.GetChild("icon").asLoader;
        var lblCount = item.asCom.GetChild("lblCount").asTextField;

        var data = rewards[index];
        var info = ConfigItem.GetData((int)data.ItemId);
        icon.url = info.iconUrl;
        bg.url = info.qualityUrl;
        lblCount.text = MathUtil.ConvertNumWithUnit(data.Count);
        TipMgr.SetItemTip(icon, info.id);
    }

    internal void SetData(int openLevel, NetSeasonPassesInfoResult curSeasonPassesInfo)
    {
        canGetRewards = new List<BaseNetItemInfo>();
        totalRewards = new List<BaseNetItemInfo>();
        var rewards = curSeasonPassesInfo.LevelRewardList0.ToList();
        if (curSeasonPassesInfo.BuyType == 2)
        {
            rewards.AddRange(curSeasonPassesInfo.LevelRewardList2);
        }
        else
        {
            rewards.AddRange(curSeasonPassesInfo.LevelRewardList1);
        }
        foreach (var reward in rewards)
        {
            bool isExist = false;
            if (reward.Status == 2)
                continue;
            /*
            foreach (var existReward in totalRewards)
            {
                for(var i = 0; i < reward.ItemList.Count; i++)
                {
                    if(existReward.ItemId == reward.ItemList[i].ItemId)
                    {
                       
                        existReward.Count += reward.ItemList[i].Count;
                        isExist = true;
                    }
                }
            
            }*/

            if (!isExist)
            {
                for(var i = 0; i < reward.ItemList.Count; i++)
                {
                    var hasSame = false;
                    for (var j = 0; j < totalRewards.Count; j++)
                    {
                        if (reward.ItemList[i].ItemId == totalRewards[j].ItemId)
                        {
                            hasSame = true;
                            totalRewards[j].Count += reward.ItemList[i].Count;
                        }
                    }
                    if(!hasSame)
                        totalRewards.Add(new BaseNetItemInfo() { ItemId = reward.ItemList[i].ItemId, Count = reward.ItemList[i].Count });
                }
            }

            if (reward.Level <= openLevel)
            {
                isExist = false;
                /*
                foreach (var existReward in canGetRewards)
                {
                    for (var i = 0; i < reward.ItemList.Count; i++)
                    {
                        if (existReward.ItemId == reward.ItemList[i].ItemId)
                        {
                            existReward.Count += reward.ItemList[i].Count;
                            isExist = true;
                        }
                    }
                }*/

                if (!isExist)
                {
                    for (var i = 0; i < reward.ItemList.Count; i++)
                    {
                        var hasSame = false;
                        for(var j = 0; j < canGetRewards.Count; j++)
                        {
                            if (canGetRewards[j].ItemId == reward.ItemList[i].ItemId)
                            {
                                hasSame = true;
                                canGetRewards[j].Count += reward.ItemList[i].Count;
                            }
                        }
                        if (!hasSame)
                        {
                            canGetRewards.Add(new BaseNetItemInfo() { ItemId = reward.ItemList[i].ItemId, Count = reward.ItemList[i].Count });
                        }
                    }
                }
            }
        }
        canGetRewards.Sort((a1, a2) =>
        {
            var item1 = ConfigItem.GetData((int)a1.ItemId);
            var item2 = ConfigItem.GetData((int)a2.ItemId);

            return (int)(item2.quality * 100000000 - a2.Count) - (int)(item1.quality * 100000000 - a1.Count);
        });

        totalRewards.Sort((a1, a2) =>
        {
            var item1 = ConfigItem.GetData((int)a1.ItemId);
            var item2 = ConfigItem.GetData((int)a2.ItemId);

            return (int)(item2.quality * 100000000 - a2.Count) - (int)(item1.quality * 100000000 - a1.Count);
        });
        listNowReward.numItems = canGetRewards.Count;
        listTotalReward.numItems = totalRewards.Count;
        lblNotReward.visible = canGetRewards.Count <= 0;

        btnBuyHigh.onClick.Clear();
        btnBuyHigh.onClick.Add(() =>
        {
            Create((SeasonPassesVipPanel panel) =>
            {
                panel.SetData(curSeasonPassesInfo);
            });
            Hide();
        });
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
        }
    }
}
