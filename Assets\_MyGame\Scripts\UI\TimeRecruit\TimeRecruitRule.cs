using System;
using System.Collections.Generic;
using DashGame;
using FairyGUI;
using Proto.LogicData;
using UnityEngine;

public class TimeRecruitRule : Panel
{
    private GList listPr;
    private DictList<int, List<BaseNetItemInfo>> heroInfos = new DictList<int, List<BaseNetItemInfo>>();
    public TimeRecruitRule()
    {
        packName = "TimeRecruit";
        compName = "RecruitRule";
        modal = true;
    }

    internal void SetData(List<BaseNetItemInfo> dropItemInfo)
    {
        if (dropItemInfo == null || dropItemInfo.Count == 0)
            return;

        heroInfos.Clear();
        foreach (var item in dropItemInfo)
        {
            if (item != null)
            {
                var quality = ConfigItem.GetData((int)item.ItemId).quality;
                if (heroInfos.ContainsKey((int)item.Param1))
                {
                    heroInfos.GetValueByKey((int)item.Param1).Add(item);
                }
                else
                {
                    var list = new List<BaseNetItemInfo>();
                    list.Add(item);
                    heroInfos.Add((int)item.Param1, list);
                }
            }
        }
        heroInfos.GetKeys().Sort((a1, a2) => {

            return a2 - a1;
        });
        listPr = contentPane.GetChild("listPr").asList;
        listPr.itemRenderer = UpdatePrItem;
        listPr.numItems = heroInfos.GetKeys().Count;

        //rate 1解锁 0没解锁  param1 group

    }
    private void UpdatePrItem(int index, GObject obj)
    {
        var key = heroInfos.GetKeyByIndex(index);
        var heros = heroInfos.GetValueByKey(key);
        var bg = obj.asCom.GetChild("bg").asLoader;
        var lblProbalility = obj.asCom.GetChild("lblProbalility").asTextField;
        var listHero = obj.asCom.GetChild("listHero").asList;
        listHero.itemRenderer = OnUpdateHeroItem;
        bg.url = GetCurPackRes("image_ruleInfoBg_" + key);
        var probalility = 0;
        var heroList = new List<BaseNetItemInfo>();
        foreach (var item in heros)
        {
            if (item != null)
            {
                probalility += (int)item.Param;
            }

            if (item.Rate == 1)
            {
                heroList.Add(item);
            }
        }

        lblProbalility.text = LangUtil.GetText("txtHeroProbalility", (probalility * 100 * 0.0001).ToString()) ;
        listHero.data = heroList;
        // heros.Sort((a1, a2) => { 
        //     return a2.UpPool - a1.UpPool;
        // });
        listHero.numItems = heroList.Count;
        obj.height = Mathf.CeilToInt(listHero.numItems / 5f) * (listHero._rawHeight + listHero.lineGap) + listHero.y;
    }

    private void OnUpdateHeroItem(int index, GObject obj)
    {
        var data = (obj.parent.data as List<BaseNetItemInfo>)[index];
        var icon = obj.asCom.GetChild("icon").asLoader;
        var iconBg = obj.asCom.GetChild("iconBg").asLoader;
        var title = obj.asCom.GetChild("title").asTextField;
        var rewardInfo = ConfigItem.GetData((int)data.ItemId);
        icon.url = rewardInfo.iconUrl;
        iconBg.url = rewardInfo.qualityUrl;
        title.text = data.Count.ToString();
        TipMgr.SetItemTip(icon, rewardInfo.id);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
        }
    }
}
