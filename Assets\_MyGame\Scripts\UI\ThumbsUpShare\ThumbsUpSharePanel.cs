using Common.NetMsg;
using FairyGUI;
using Google.Protobuf.Collections;
using Proto.LogicData;
using UnityEngine;

public class ThumbsUpSharePanel : Panel
{
    private GList listReward;
    private GList listTab;

    private GButton btnShare;
    private GButton btnThumbs;
    private GButton btnGetReward;

    BaseNetActivityInfo thumbsInfo;
    BaseNetActivityInfo shareInfo;

    private int curIndex;

    private RepeatedField<BaseNetActivityInfo> activityList;

    public ThumbsUpSharePanel()
    {
        packName = "ThumbsUpShare";
        compName = "ThumbsUpSharePanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        listReward = contentPane.GetChild("listReward").asList;
        listReward.itemRenderer = UpdateListReward;

        listTab = contentPane.GetChild("listTab").asList;
        listTab.itemRenderer = UpdateListTabItem;
        listTab.onClickItem.Add(OnClickTabItem);

        btnShare = contentPane.GetChild("btnShare").asButton;
        btnThumbs = contentPane.GetChild("btnThumbs").asButton;
        btnGetReward = contentPane.GetChild("btnGetReward").asButton;

        Request();
    }

    private void Request()
    {
        NetMgr.RequestActivitiesInfo((int)ActivityDisplayUiEnum.ThumbsUpShare);
    }

    protected override void OnMessageRecieve(NetMessage msg)
    {
        var cmd = msg.GetCmd();
        switch (cmd)
        {
            case NetMsg.S2CRequestActivitiesInfoResult:
                var tempdataInfo = msg.Parse<RequestActivitiesInfoResult>();
                if (tempdataInfo.DisplayUi == (int)ActivityDisplayUiEnum.ThumbsUpShare)
                {
                    if (tempdataInfo.ActivityList.Count > 0)
                    {
                        SetData(tempdataInfo.ActivityList);
                    }
                    else
                    {
                        Hide();
                    }
                }
                break;
            case NetMsg.S2CGetActivityRewardResult:
                var rewardResult = msg.Parse<RequestGetActivityRewardResult>();
                if (LangUtil.CheckResult(rewardResult.Result) && rewardResult.DisplayUi == (int)ActivityDisplayUiEnum.ThumbsUpShare)
                {
                    TipMgr.ShowReward(rewardResult.ItemList, () =>
                    {
                        Request();
                    });
                }
                break;
            case NetMsg.S2CPlayerShareResult:
                var shareResult = msg.Parse<NetPlayerShareResult>();
                if (shareResult.Result == ErrorCode.Success)
                {
                    Request();
                }
                else
                {
                    TipMgr.ShowErrorCodeTip(shareResult.Result);
                }
                break;
        }
    }

    private void SetData(RepeatedField<BaseNetActivityInfo> activityList)
    {
        if (activityList == null || activityList.Count == 0)
        {
            Hide();
            return;
        }
        this.activityList = activityList;
        for (int i = 0; i < activityList.Count; i++)
        {
            var activityInfo = activityList[i];
            if (activityInfo.DisplayTab == 0)
            {
                shareInfo = activityInfo;
            }
            else if (activityInfo.DisplayTab == 1)
            {
                thumbsInfo = activityInfo;
            }
        }
        listTab.data = activityList;
        listTab.numItems = activityList.Count;
        listTab.visible = activityList.Count > 1;
        OnClickTabItem(null);
    }

    private void UpdateListTabItem(int index, GObject obj)
    {
        var data = (obj.parent.data as RepeatedField<BaseNetActivityInfo>)[index];
        var title = obj.asCom.GetChild("title").asTextField;
        title.text = LangUtil.GetText("txtThumbsUpShareTabTitle" + data.DisplayTab);
        var imgRedDot = obj.asCom.GetChild("imgRedDot");
        imgRedDot.visible = !data.IsSendReward;
    }

    private void UpdateListReward(int index, GObject obj)
    {
        var data = activityList[curIndex].RewardItemList[index];
        var icon = obj.asCom.GetChild("icon").asLoader;
        var imgQualityBg = obj.asCom.GetChild("imgQualityBg").asLoader;
        var lblCount = obj.asCom.GetChild("lblCount").asTextField;

        TipMgr.SetItemTip(icon, (int)data.ItemId);

        InfoItem info = ConfigItem.GetData((int)data.ItemId);
        if (info == null)
        {
            return;
        }

        if (icon != null)
        {
            icon.url = info.iconUrl;
        }
        if (imgQualityBg != null)
        {
            imgQualityBg.url = info.qualityUrl;
        }
        if (lblCount != null)
        {
            lblCount.text = MathUtil.ConvertNumWithUnit(data.Count);
        }
    }

    private void OnClickTabItem(EventContext context)
    {
        curIndex = listTab.selectedIndex < 0 ? 0 : listTab.selectedIndex;
        listTab.selectedIndex = curIndex;
        var activityInfo = activityList[curIndex];
        var ctrl = contentPane.GetController("c1");
        ctrl.selectedIndex = activityInfo.DisplayStyle;
        listReward.numItems = activityInfo.RewardItemList.Count;
        ChangeButton();
    }

    private void ChangeButton()
    {
        var lblClaimed = contentPane.GetChild("lblClaimed").asTextField;
        lblClaimed.visible = false;

        var activitInfo = activityList[curIndex];
        btnThumbs.visible = activitInfo.CanGetReward == 0 && !activitInfo.IsSendReward;
        btnShare.visible = activitInfo.CanGetReward != 0 && !activitInfo.IsSendReward;
        btnGetReward.visible = activitInfo.CanGetReward == 0 && !activitInfo.IsSendReward;
        lblClaimed.visible = activitInfo.IsSendReward;
        if (activitInfo.DisplayTab == 1)
        {
            btnGetReward.visible = !btnThumbs.visible;
        }
    }

    private void OnShare()
    {
        Platform.GetInstance().Share(ShareIds.ThumbsUpShare, Session.playerLobbyInfo.OwnerinviteCode, () =>
        {
            NetMgr.C2SPlayerShare(ShareIds.ThumbsUpShare);
        });
    }

    private void OnThumbs()
    {
        Platform.GetInstance().OpenUrl("");
        if (thumbsInfo != null)
        {
            thumbsInfo.CanGetReward = 1;
        }
        ChangeButton();
    }

    private void OnGetReward()
    {
        var activityInfo = activityList[curIndex];
        NetMgr.GetActivityReward(activityInfo.Id);
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                Hide();
                break;
            case "btnShare":
                OnShare();
                break;
            case "btnThumbs":
                OnThumbs();
                break;
            case "btnGetReward":
                OnGetReward();
                break;
        }
    }
}