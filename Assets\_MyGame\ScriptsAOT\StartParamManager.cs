using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;

[Serializable]
public class StartParamResponse
{
    public int code;
    public StartParam data;
    public string msg;
}

[Serializable]
public class StartParam
{
    public string channelType;
    public string platformId;
    public string ver;
    public string cdnUrl;

    // 登录隐私设置
    public int loginPrivacy;

    // 支付相关
    public int paySandbox;
    public string callUrl;

    // 商城显示控制
    public int isShowShop;

    // 服务器信息
    public string serverCode;
    public string serverName;
    public int serverType; // 1=正式服，2=测试服 3=审核服
    public string webUrl;
    public string loginUrl;

    // 上报控制
    public int ignoreReport; // 0：上报  1：不上报

    // 其他参数
    public int status;
    public int miniGameId;
}

public class StartParamManager
{
    private static StartParamManager _instance;
    public static StartParamManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new StartParamManager();
            }
            return _instance;
        }
    }

    private StartParam startParam;
    public StartParam CurrentParam => startParam;

    /// <summary>
    /// 获取启动参数（带重试功能）
    /// </summary>
    /// <param name="url">参数获取URL</param>
    /// <param name="timeout">超时时间（秒）</param>
    /// <param name="maxRetries">最大重试次数</param>
    /// <param name="retryDelay">重试间隔时间（秒）</param>
    /// <param name="onComplete">完成回调</param>
    /// <param name="onError">错误回调</param>
    /// <returns></returns>
    public IEnumerator GetStartParam(string url, int timeout = 30, int maxRetries = 3, float retryDelay = 2f, System.Action<StartParam> onComplete = null, System.Action<string> onError = null)
    {
        int currentRetry = 0;
        string lastError = "";

        while (currentRetry <= maxRetries)
        {
            Debug.Log($"[Start] ({currentRetry + 1}/{maxRetries + 1}) URL: {url}");

            bool requestSuccess = false;

            using (UnityWebRequest request = UnityWebRequest.Get(url))
            {
                request.timeout = timeout;
                request.SetRequestHeader("Content-Type", "application/json");

                // 发送请求
                yield return request.SendWebRequest();

                // 检查请求结果
                if (request.result == UnityWebRequest.Result.Success)
                {
                    try
                    {
                        string jsonResponse = request.downloadHandler.text;
                        Debug.Log($"[Start] rspd: {jsonResponse}");

                        StartParamResponse response = JsonUtility.FromJson<StartParamResponse>(jsonResponse);
                        if (response != null && response.code == 0 && response.data != null)
                        {
                            startParam = response.data;
                            onComplete?.Invoke(startParam);
                            requestSuccess = true;
                        }
                        else
                        {
                            string errorMsg = response?.msg ?? "未知错误";
                            int errorCode = response?.code ?? -1;
                            lastError = $"服务器返回错误 - Code: {errorCode}, Message: {errorMsg}";
                        }
                    }
                    catch (Exception e)
                    {
                        lastError = $"解析JSON失败: {e.Message}";
                    }
                }
                else
                {
                    lastError = $"请求失败: {request.error}";
                }
            }

            // 如果请求成功，直接返回
            if (requestSuccess)
            {
                yield break;
            }

            // 如果还有重试次数，等待后重试
            if (currentRetry < maxRetries)
            {
                Debug.LogWarning($"[Start] {retryDelay}秒后重试...");
                yield return new WaitForSeconds(retryDelay);
                currentRetry++;
            }
            else
            {
                onError?.Invoke($"参数获取失败{lastError}");
                break;
            }
        }
    }

    /// <summary>
    /// 检查参数是否已加载
    /// </summary>
    /// <returns></returns>
    public bool IsParamLoaded()
    {
        return startParam != null;
    }
}
