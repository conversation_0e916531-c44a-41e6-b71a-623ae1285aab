public class ConfigChatDeco : ConfigManager
{
    public static InfoChatDeco GetData(string decoID)
    {
        if (!int.TryParse(decoID, out int decoIDInt))
        {
            decoIDInt = 0;
        }
        var info = ConfigHelper.GetManager<ConfigChatDeco>().GetData<InfoChatDeco>(decoIDInt);
        if (info == null)
        {
            info = ConfigHelper.GetManager<ConfigChatDeco>().GetData<InfoChatDeco>(0);
        }
        return info;
    }
}