using LitJson;
using System;

[Serializable]
public class InfoSticker : ConfigData
{
    public int index;
    public int groupID;
    public int id;
    public string name;
    public int type; // 1: emoji, 2: 大表情
    public int effectType; // 0: 静态图, 1: 序列帧
    public string effectID; // 对应特效名

    public override object key { get => index; protected set => base.key = value; }

    public override void Parse(JsonData data)
    {
        index = JsonUtil.ToInt(data, "index");
        groupID = JsonUtil.ToInt(data, "groupId");
        id = JsonUtil.ToInt(data, "id");
        name = JsonUtil.ToString(data, "name");
        type = JsonUtil.ToInt(data, "type");
        effectType = JsonUtil.ToInt(data, "effectType");
        effectID = JsonUtil.ToString(data, "effectId");
    }
}