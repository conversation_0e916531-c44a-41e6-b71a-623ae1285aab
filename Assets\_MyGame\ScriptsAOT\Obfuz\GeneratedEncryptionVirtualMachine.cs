﻿/// This file is auto-generated by <PERSON><PERSON><PERSON><PERSON>. Do not modify it.
///
/// Version: 0
/// SecretKey: Obfuz
/// OpCodeCount: 256

namespace Obfuz.EncryptionVM
{
    public class GeneratedEncryptionVirtualMachine : Obfuz.EncryptorBase
    {

        private const int kOpCodeBits = 8;

        private const int kOpCodeCount = 256;

        private const int kOpCodeMask = 255;



        private readonly int[] _secretKey;

        public GeneratedEncryptionVirtualMachine(byte[] secretKey)
        {
            this._secretKey = ConvertToIntKey(secretKey);
        }

        public override int OpCodeCount => kOpCodeCount;

        public override int Encrypt(int value, int opts, int salt)
        {
            uint uopts = (uint)opts;
            uint revertOps = 0;
            while (uopts != 0)
            {
                uint opCode = uopts & kOpCodeMask;
                revertOps <<= kOpCodeBits;
                revertOps |= opCode;
                uopts >>= kOpCodeBits;
            }

            while (revertOps != 0)
            {
                uint opCode = revertOps & kOpCodeMask;
                value = ExecuteEncrypt(value, (int)opCode, salt);
                revertOps >>= kOpCodeBits;
            }
            return value;
        }

        public override int Decrypt(int value, int opts, int salt)
        {
            uint uopts = (uint)opts;
            while (uopts != 0)
            {
                uint opCode = uopts & kOpCodeMask;
                value = ExecuteDecrypt(value, (int)opCode, salt);
                uopts >>= kOpCodeBits;
            }
            return value;
        }


        private int ExecuteEncrypt(int value, int opCode, int salt)
        {
            switch (opCode)
            {
               case 0:
                {
                    // MultipleInstruction
                    value = value *  598188269 + _secretKey[84] + salt;
                    return value;
                }
               case 1:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1350058129 + _secretKey[136];
                    uint part1 = (uint)value << 4;
                    uint part2 = (uint)value >> (32 - 4);
                    value = (int)(part1 | part2);
                    value ^= 1817406469 ^ salt;
                    return value;
                }
               case 2:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1144218503 + _secretKey[246];
                    value ^= -1498541961 ^ salt;
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 3:
                {
                    // AddRotateXorInstruction
                    value += -1207833585 + _secretKey[26];
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= 29411710 ^ salt;
                    return value;
                }
               case 4:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = ((int)(part1 | part2) ^ _secretKey[85]) + salt;
                    return value;
                }
               case 5:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1447238259 + _secretKey[165];
                    value ^= 86149918 ^ salt;
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 6:
                {
                    // AddRotateXorInstruction
                    value += -1856354856 + _secretKey[178];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    value ^= -1262500500 ^ salt;
                    return value;
                }
               case 7:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[53]) + salt) ^ 665464645;
                    return value;
                }
               case 8:
                {
                    // XorMultipleRotateInstruction
                    value ^= -1044567439 ^ salt;
                    value = value * -1860181607 + _secretKey[206];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 9:
                {
                    // MultipleInstruction
                    value = value *  522878123 + _secretKey[196] + salt;
                    return value;
                }
               case 10:
                {
                    // XorAddRotateInstruction
                    value ^= -755609206 ^ salt;
                    value += -1035239660 + _secretKey[199];
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 11:
                {
                    // AddInstruction
                    value = ((value + _secretKey[89]) ^ salt) + -1177184477;
                    return value;
                }
               case 12:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = ((int)(part1 | part2) ^ _secretKey[23]) + salt;
                    return value;
                }
               case 13:
                {
                    // XorMultipleRotateInstruction
                    value ^= -1510419150 ^ salt;
                    value = value * -93709937 + _secretKey[68];
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 14:
                {
                    // MultipleRotateXorInstruction
                    value = value * 2015893433 + _secretKey[156];
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = (int)(part1 | part2);
                    value ^= -1134639492 ^ salt;
                    return value;
                }
               case 15:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = ((int)(part1 | part2) ^ _secretKey[95]) + salt;
                    return value;
                }
               case 16:
                {
                    // XorMultipleRotateInstruction
                    value ^= -759315565 ^ salt;
                    value = value * -436699251 + _secretKey[79];
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 17:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1266530571 + _secretKey[75];
                    uint part1 = (uint)value << 2;
                    uint part2 = (uint)value >> (32 - 2);
                    value = (int)(part1 | part2);
                    value ^= -3885258 ^ salt;
                    return value;
                }
               case 18:
                {
                    // MultipleXorRotateInstruction
                    value = value * -259404705 + _secretKey[153];
                    value ^= 1207613963 ^ salt;
                    uint part1 = (uint)value << 9;
                    uint part2 = (uint)value >> (32 - 9);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 19:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1042624059 + _secretKey[86];
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= 792769043 ^ salt;
                    return value;
                }
               case 20:
                {
                    // AddInstruction
                    value = ((value + _secretKey[194]) ^ salt) + -512520382;
                    return value;
                }
               case 21:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[195]) + salt) ^ -1864951858;
                    return value;
                }
               case 22:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = ((int)(part1 | part2) ^ _secretKey[2]) + salt;
                    return value;
                }
               case 23:
                {
                    // MultipleInstruction
                    value = value *  -463118297 + _secretKey[203] + salt;
                    return value;
                }
               case 24:
                {
                    // AddInstruction
                    value = ((value + _secretKey[110]) ^ salt) + 2026667919;
                    return value;
                }
               case 25:
                {
                    // AddXorRotateInstruction
                    value += 848657810 + _secretKey[133];
                    value ^= 392708821 ^ salt;
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 26:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = ((int)(part1 | part2) ^ _secretKey[93]) + salt;
                    return value;
                }
               case 27:
                {
                    // XorAddRotateInstruction
                    value ^= 1294662302 ^ salt;
                    value += 1284012732 + _secretKey[58];
                    uint part1 = (uint)value << 30;
                    uint part2 = (uint)value >> (32 - 30);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 28:
                {
                    // XorMultipleRotateInstruction
                    value ^= -1438081752 ^ salt;
                    value = value * -1171400509 + _secretKey[138];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 29:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = ((int)(part1 | part2) ^ _secretKey[240]) + salt;
                    return value;
                }
               case 30:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1892661727 + _secretKey[215];
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = (int)(part1 | part2);
                    value ^= 1594594445 ^ salt;
                    return value;
                }
               case 31:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = ((int)(part1 | part2) ^ _secretKey[49]) + salt;
                    return value;
                }
               case 32:
                {
                    // XorAddRotateInstruction
                    value ^= 226193183 ^ salt;
                    value += -1038657413 + _secretKey[247];
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 33:
                {
                    // XorAddRotateInstruction
                    value ^= -484591087 ^ salt;
                    value += 459902223 + _secretKey[252];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 34:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1382643267 + _secretKey[211];
                    value ^= 1186351980 ^ salt;
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 35:
                {
                    // XorAddRotateInstruction
                    value ^= 1658142493 ^ salt;
                    value += -283413931 + _secretKey[235];
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 36:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[90]) + salt) ^ -1892941953;
                    return value;
                }
               case 37:
                {
                    // XorMultipleRotateInstruction
                    value ^= -1297440001 ^ salt;
                    value = value * -1166749617 + _secretKey[111];
                    uint part1 = (uint)value << 31;
                    uint part2 = (uint)value >> (32 - 31);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 38:
                {
                    // MultipleRotateXorInstruction
                    value = value * -375439505 + _secretKey[213];
                    uint part1 = (uint)value << 31;
                    uint part2 = (uint)value >> (32 - 31);
                    value = (int)(part1 | part2);
                    value ^= 1353158598 ^ salt;
                    return value;
                }
               case 39:
                {
                    // XorAddRotateInstruction
                    value ^= -934836680 ^ salt;
                    value += 873171360 + _secretKey[72];
                    uint part1 = (uint)value << 20;
                    uint part2 = (uint)value >> (32 - 20);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 40:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1427441479 + _secretKey[140];
                    value ^= -28088263 ^ salt;
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 41:
                {
                    // AddRotateXorInstruction
                    value += 969234286 + _secretKey[116];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    value ^= -312111197 ^ salt;
                    return value;
                }
               case 42:
                {
                    // MultipleInstruction
                    value = value *  -1130955295 + _secretKey[29] + salt;
                    return value;
                }
               case 43:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = ((int)(part1 | part2) ^ _secretKey[1]) + salt;
                    return value;
                }
               case 44:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 4;
                    uint part2 = (uint)value >> (32 - 4);
                    value = ((int)(part1 | part2) ^ _secretKey[184]) + salt;
                    return value;
                }
               case 45:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1782633617 + _secretKey[199];
                    value ^= 1068062556 ^ salt;
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 46:
                {
                    // XorMultipleRotateInstruction
                    value ^= 196245895 ^ salt;
                    value = value * -1852817781 + _secretKey[200];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 47:
                {
                    // MultipleXorRotateInstruction
                    value = value * -50587953 + _secretKey[10];
                    value ^= -1964107221 ^ salt;
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 48:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1216851115 + _secretKey[238];
                    value ^= -38668552 ^ salt;
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 49:
                {
                    // MultipleInstruction
                    value = value *  -2133817615 + _secretKey[172] + salt;
                    return value;
                }
               case 50:
                {
                    // AddXorRotateInstruction
                    value += 1412414820 + _secretKey[219];
                    value ^= -2098495662 ^ salt;
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 51:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = ((int)(part1 | part2) ^ _secretKey[97]) + salt;
                    return value;
                }
               case 52:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1791837593 + _secretKey[63];
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = (int)(part1 | part2);
                    value ^= -21948406 ^ salt;
                    return value;
                }
               case 53:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = ((int)(part1 | part2) ^ _secretKey[221]) + salt;
                    return value;
                }
               case 54:
                {
                    // MultipleInstruction
                    value = value *  -1220403071 + _secretKey[96] + salt;
                    return value;
                }
               case 55:
                {
                    // MultipleRotateXorInstruction
                    value = value * -867896207 + _secretKey[29];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    value ^= -1022882984 ^ salt;
                    return value;
                }
               case 56:
                {
                    // XorMultipleRotateInstruction
                    value ^= 2122077674 ^ salt;
                    value = value * 957274637 + _secretKey[235];
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 57:
                {
                    // AddRotateXorInstruction
                    value += -503448718 + _secretKey[175];
                    uint part1 = (uint)value << 21;
                    uint part2 = (uint)value >> (32 - 21);
                    value = (int)(part1 | part2);
                    value ^= -720824840 ^ salt;
                    return value;
                }
               case 58:
                {
                    // MultipleInstruction
                    value = value *  -280528271 + _secretKey[231] + salt;
                    return value;
                }
               case 59:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = ((int)(part1 | part2) ^ _secretKey[75]) + salt;
                    return value;
                }
               case 60:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1403438719 + _secretKey[171];
                    uint part1 = (uint)value << 27;
                    uint part2 = (uint)value >> (32 - 27);
                    value = (int)(part1 | part2);
                    value ^= 1420744071 ^ salt;
                    return value;
                }
               case 61:
                {
                    // XorMultipleRotateInstruction
                    value ^= 1366253139 ^ salt;
                    value = value * -1583570963 + _secretKey[84];
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 62:
                {
                    // MultipleXorRotateInstruction
                    value = value * 24444809 + _secretKey[132];
                    value ^= -1974823163 ^ salt;
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 63:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 22;
                    uint part2 = (uint)value >> (32 - 22);
                    value = ((int)(part1 | part2) ^ _secretKey[119]) + salt;
                    return value;
                }
               case 64:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[15]) + salt) ^ 1262347216;
                    return value;
                }
               case 65:
                {
                    // AddInstruction
                    value = ((value + _secretKey[126]) ^ salt) + 1780280992;
                    return value;
                }
               case 66:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1871888237 + _secretKey[85];
                    value ^= -1198162446 ^ salt;
                    uint part1 = (uint)value << 13;
                    uint part2 = (uint)value >> (32 - 13);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 67:
                {
                    // MultipleInstruction
                    value = value *  931252767 + _secretKey[128] + salt;
                    return value;
                }
               case 68:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = ((int)(part1 | part2) ^ _secretKey[178]) + salt;
                    return value;
                }
               case 69:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[61]) + salt) ^ 1649427052;
                    return value;
                }
               case 70:
                {
                    // MultipleRotateXorInstruction
                    value = value * -100486091 + _secretKey[83];
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    value ^= 849172121 ^ salt;
                    return value;
                }
               case 71:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = ((int)(part1 | part2) ^ _secretKey[169]) + salt;
                    return value;
                }
               case 72:
                {
                    // XorAddRotateInstruction
                    value ^= 1068287172 ^ salt;
                    value += -2092062916 + _secretKey[138];
                    uint part1 = (uint)value << 20;
                    uint part2 = (uint)value >> (32 - 20);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 73:
                {
                    // MultipleInstruction
                    value = value *  -729679733 + _secretKey[34] + salt;
                    return value;
                }
               case 74:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1624925351 + _secretKey[30];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    value ^= -145743337 ^ salt;
                    return value;
                }
               case 75:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1768166349 + _secretKey[142];
                    uint part1 = (uint)value << 4;
                    uint part2 = (uint)value >> (32 - 4);
                    value = (int)(part1 | part2);
                    value ^= 280941267 ^ salt;
                    return value;
                }
               case 76:
                {
                    // MultipleInstruction
                    value = value *  -1708700487 + _secretKey[156] + salt;
                    return value;
                }
               case 77:
                {
                    // AddRotateXorInstruction
                    value += 314625916 + _secretKey[192];
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = (int)(part1 | part2);
                    value ^= 827331935 ^ salt;
                    return value;
                }
               case 78:
                {
                    // MultipleInstruction
                    value = value *  875189907 + _secretKey[141] + salt;
                    return value;
                }
               case 79:
                {
                    // AddXorRotateInstruction
                    value += 1607953190 + _secretKey[133];
                    value ^= -11549173 ^ salt;
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 80:
                {
                    // XorAddRotateInstruction
                    value ^= -1454482890 ^ salt;
                    value += 687186546 + _secretKey[95];
                    uint part1 = (uint)value << 25;
                    uint part2 = (uint)value >> (32 - 25);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 81:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[195]) + salt) ^ 814860713;
                    return value;
                }
               case 82:
                {
                    // AddRotateXorInstruction
                    value += 1998643542 + _secretKey[171];
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = (int)(part1 | part2);
                    value ^= 213310246 ^ salt;
                    return value;
                }
               case 83:
                {
                    // AddRotateXorInstruction
                    value += 542686146 + _secretKey[249];
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    value ^= -696314173 ^ salt;
                    return value;
                }
               case 84:
                {
                    // AddRotateXorInstruction
                    value += 1734820207 + _secretKey[2];
                    uint part1 = (uint)value << 9;
                    uint part2 = (uint)value >> (32 - 9);
                    value = (int)(part1 | part2);
                    value ^= 118718247 ^ salt;
                    return value;
                }
               case 85:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[143]) + salt) ^ 1553710234;
                    return value;
                }
               case 86:
                {
                    // AddRotateXorInstruction
                    value += -217984331 + _secretKey[146];
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = (int)(part1 | part2);
                    value ^= -1402843691 ^ salt;
                    return value;
                }
               case 87:
                {
                    // MultipleInstruction
                    value = value *  1078374119 + _secretKey[5] + salt;
                    return value;
                }
               case 88:
                {
                    // MultipleRotateXorInstruction
                    value = value * -975647447 + _secretKey[158];
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = (int)(part1 | part2);
                    value ^= -2044505542 ^ salt;
                    return value;
                }
               case 89:
                {
                    // AddInstruction
                    value = ((value + _secretKey[40]) ^ salt) + -1213654475;
                    return value;
                }
               case 90:
                {
                    // XorMultipleRotateInstruction
                    value ^= -1886972278 ^ salt;
                    value = value * 275510141 + _secretKey[206];
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 91:
                {
                    // AddRotateXorInstruction
                    value += -1724625239 + _secretKey[223];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    value ^= -979249928 ^ salt;
                    return value;
                }
               case 92:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[83]) + salt) ^ -1104541704;
                    return value;
                }
               case 93:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[31]) + salt) ^ 311150152;
                    return value;
                }
               case 94:
                {
                    // XorMultipleRotateInstruction
                    value ^= 116496631 ^ salt;
                    value = value * 13120561 + _secretKey[176];
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 95:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1932552195 + _secretKey[87];
                    uint part1 = (uint)value << 2;
                    uint part2 = (uint)value >> (32 - 2);
                    value = (int)(part1 | part2);
                    value ^= 2133438141 ^ salt;
                    return value;
                }
               case 96:
                {
                    // MultipleRotateXorInstruction
                    value = value * 126641773 + _secretKey[174];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 225535005 ^ salt;
                    return value;
                }
               case 97:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1013570837 + _secretKey[175];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    value ^= -1703839105 ^ salt;
                    return value;
                }
               case 98:
                {
                    // XorAddRotateInstruction
                    value ^= -447564571 ^ salt;
                    value += -1783079937 + _secretKey[78];
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 99:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[111]) + salt) ^ -316631669;
                    return value;
                }
               case 100:
                {
                    // XorMultipleRotateInstruction
                    value ^= -405694625 ^ salt;
                    value = value * 1711408839 + _secretKey[252];
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 101:
                {
                    // MultipleXorRotateInstruction
                    value = value * -910384311 + _secretKey[212];
                    value ^= -1551058348 ^ salt;
                    uint part1 = (uint)value << 6;
                    uint part2 = (uint)value >> (32 - 6);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 102:
                {
                    // AddInstruction
                    value = ((value + _secretKey[18]) ^ salt) + -853736135;
                    return value;
                }
               case 103:
                {
                    // AddInstruction
                    value = ((value + _secretKey[116]) ^ salt) + 1321376878;
                    return value;
                }
               case 104:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1039555235 + _secretKey[7];
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= 541697309 ^ salt;
                    return value;
                }
               case 105:
                {
                    // AddRotateXorInstruction
                    value += -822594180 + _secretKey[1];
                    uint part1 = (uint)value << 4;
                    uint part2 = (uint)value >> (32 - 4);
                    value = (int)(part1 | part2);
                    value ^= 1176608900 ^ salt;
                    return value;
                }
               case 106:
                {
                    // AddInstruction
                    value = ((value + _secretKey[110]) ^ salt) + -882893600;
                    return value;
                }
               case 107:
                {
                    // MultipleRotateXorInstruction
                    value = value * -346726819 + _secretKey[114];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    value ^= -1566546809 ^ salt;
                    return value;
                }
               case 108:
                {
                    // MultipleInstruction
                    value = value *  -981516343 + _secretKey[97] + salt;
                    return value;
                }
               case 109:
                {
                    // XorAddRotateInstruction
                    value ^= -72794161 ^ salt;
                    value += 1161425930 + _secretKey[43];
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 110:
                {
                    // AddInstruction
                    value = ((value + _secretKey[238]) ^ salt) + -1619543125;
                    return value;
                }
               case 111:
                {
                    // XorAddRotateInstruction
                    value ^= 1170459122 ^ salt;
                    value += -1766468683 + _secretKey[241];
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 112:
                {
                    // AddXorRotateInstruction
                    value += 251687012 + _secretKey[219];
                    value ^= 323019346 ^ salt;
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 113:
                {
                    // AddInstruction
                    value = ((value + _secretKey[97]) ^ salt) + -1049972438;
                    return value;
                }
               case 114:
                {
                    // XorMultipleRotateInstruction
                    value ^= -492239002 ^ salt;
                    value = value * -1496840897 + _secretKey[216];
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 115:
                {
                    // AddInstruction
                    value = ((value + _secretKey[221]) ^ salt) + -748126329;
                    return value;
                }
               case 116:
                {
                    // AddXorRotateInstruction
                    value += -441051263 + _secretKey[96];
                    value ^= -5933889 ^ salt;
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 117:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1850062787 + _secretKey[88];
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= 543895274 ^ salt;
                    return value;
                }
               case 118:
                {
                    // MultipleInstruction
                    value = value *  -747527445 + _secretKey[140] + salt;
                    return value;
                }
               case 119:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = ((int)(part1 | part2) ^ _secretKey[175]) + salt;
                    return value;
                }
               case 120:
                {
                    // MultipleRotateXorInstruction
                    value = value * 345252089 + _secretKey[57];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 1300544743 ^ salt;
                    return value;
                }
               case 121:
                {
                    // XorAddRotateInstruction
                    value ^= -547380749 ^ salt;
                    value += 678597707 + _secretKey[119];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 122:
                {
                    // XorMultipleRotateInstruction
                    value ^= 683601851 ^ salt;
                    value = value * -145367929 + _secretKey[191];
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 123:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 20;
                    uint part2 = (uint)value >> (32 - 20);
                    value = ((int)(part1 | part2) ^ _secretKey[15]) + salt;
                    return value;
                }
               case 124:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 8;
                    uint part2 = (uint)value >> (32 - 8);
                    value = ((int)(part1 | part2) ^ _secretKey[132]) + salt;
                    return value;
                }
               case 125:
                {
                    // MultipleInstruction
                    value = value *  1436085223 + _secretKey[120] + salt;
                    return value;
                }
               case 126:
                {
                    // AddInstruction
                    value = ((value + _secretKey[197]) ^ salt) + -1374139785;
                    return value;
                }
               case 127:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = ((int)(part1 | part2) ^ _secretKey[26]) + salt;
                    return value;
                }
               case 128:
                {
                    // XorAddRotateInstruction
                    value ^= 1415280510 ^ salt;
                    value += 723646816 + _secretKey[146];
                    uint part1 = (uint)value << 21;
                    uint part2 = (uint)value >> (32 - 21);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 129:
                {
                    // AddRotateXorInstruction
                    value += 1410706317 + _secretKey[165];
                    uint part1 = (uint)value << 30;
                    uint part2 = (uint)value >> (32 - 30);
                    value = (int)(part1 | part2);
                    value ^= 2132310656 ^ salt;
                    return value;
                }
               case 130:
                {
                    // XorAddRotateInstruction
                    value ^= 1264971736 ^ salt;
                    value += -483221582 + _secretKey[93];
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 131:
                {
                    // AddXorRotateInstruction
                    value += -1872731835 + _secretKey[53];
                    value ^= 1531807059 ^ salt;
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 132:
                {
                    // AddXorRotateInstruction
                    value += 264114638 + _secretKey[240];
                    value ^= 306666665 ^ salt;
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 133:
                {
                    // AddRotateXorInstruction
                    value += 824712252 + _secretKey[138];
                    uint part1 = (uint)value << 20;
                    uint part2 = (uint)value >> (32 - 20);
                    value = (int)(part1 | part2);
                    value ^= -1266414649 ^ salt;
                    return value;
                }
               case 134:
                {
                    // AddRotateXorInstruction
                    value += -766337246 + _secretKey[35];
                    uint part1 = (uint)value << 25;
                    uint part2 = (uint)value >> (32 - 25);
                    value = (int)(part1 | part2);
                    value ^= 2133737246 ^ salt;
                    return value;
                }
               case 135:
                {
                    // AddXorRotateInstruction
                    value += -1997614825 + _secretKey[165];
                    value ^= 1683555122 ^ salt;
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 136:
                {
                    // MultipleXorRotateInstruction
                    value = value * 278699987 + _secretKey[57];
                    value ^= -1305042504 ^ salt;
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 137:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = ((int)(part1 | part2) ^ _secretKey[192]) + salt;
                    return value;
                }
               case 138:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[135]) + salt) ^ 1715223135;
                    return value;
                }
               case 139:
                {
                    // MultipleInstruction
                    value = value *  1809423757 + _secretKey[79] + salt;
                    return value;
                }
               case 140:
                {
                    // AddRotateXorInstruction
                    value += -408450171 + _secretKey[11];
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= 806320034 ^ salt;
                    return value;
                }
               case 141:
                {
                    // AddInstruction
                    value = ((value + _secretKey[95]) ^ salt) + -433941646;
                    return value;
                }
               case 142:
                {
                    // AddXorRotateInstruction
                    value += 1775820811 + _secretKey[169];
                    value ^= 2132471747 ^ salt;
                    uint part1 = (uint)value << 26;
                    uint part2 = (uint)value >> (32 - 26);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 143:
                {
                    // MultipleXorRotateInstruction
                    value = value * 457346731 + _secretKey[19];
                    value ^= 114258470 ^ salt;
                    uint part1 = (uint)value << 2;
                    uint part2 = (uint)value >> (32 - 2);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 144:
                {
                    // AddRotateXorInstruction
                    value += -650723591 + _secretKey[206];
                    uint part1 = (uint)value << 3;
                    uint part2 = (uint)value >> (32 - 3);
                    value = (int)(part1 | part2);
                    value ^= -1181275232 ^ salt;
                    return value;
                }
               case 145:
                {
                    // MultipleInstruction
                    value = value *  1369495811 + _secretKey[169] + salt;
                    return value;
                }
               case 146:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[154]) + salt) ^ -1297404981;
                    return value;
                }
               case 147:
                {
                    // MultipleRotateXorInstruction
                    value = value * 787548271 + _secretKey[181];
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = (int)(part1 | part2);
                    value ^= 801710213 ^ salt;
                    return value;
                }
               case 148:
                {
                    // AddXorRotateInstruction
                    value += -1933121809 + _secretKey[230];
                    value ^= 1566976773 ^ salt;
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 149:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 30;
                    uint part2 = (uint)value >> (32 - 30);
                    value = ((int)(part1 | part2) ^ _secretKey[188]) + salt;
                    return value;
                }
               case 150:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 30;
                    uint part2 = (uint)value >> (32 - 30);
                    value = ((int)(part1 | part2) ^ _secretKey[53]) + salt;
                    return value;
                }
               case 151:
                {
                    // AddInstruction
                    value = ((value + _secretKey[138]) ^ salt) + -2119615805;
                    return value;
                }
               case 152:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1289692111 + _secretKey[102];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 221292457 ^ salt;
                    return value;
                }
               case 153:
                {
                    // MultipleRotateXorInstruction
                    value = value * -414757417 + _secretKey[248];
                    uint part1 = (uint)value << 13;
                    uint part2 = (uint)value >> (32 - 13);
                    value = (int)(part1 | part2);
                    value ^= 1486712056 ^ salt;
                    return value;
                }
               case 154:
                {
                    // MultipleInstruction
                    value = value *  1625437745 + _secretKey[72] + salt;
                    return value;
                }
               case 155:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1500723835 + _secretKey[247];
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    value ^= -81016400 ^ salt;
                    return value;
                }
               case 156:
                {
                    // MultipleRotateXorInstruction
                    value = value * -939493617 + _secretKey[252];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    value ^= -1187848798 ^ salt;
                    return value;
                }
               case 157:
                {
                    // AddXorRotateInstruction
                    value += 605454035 + _secretKey[108];
                    value ^= 1185916334 ^ salt;
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 158:
                {
                    // AddXorRotateInstruction
                    value += 2112611413 + _secretKey[235];
                    value ^= -451761745 ^ salt;
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 159:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[229]) + salt) ^ 1660696922;
                    return value;
                }
               case 160:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1280312911 + _secretKey[111];
                    uint part1 = (uint)value << 31;
                    uint part2 = (uint)value >> (32 - 31);
                    value = (int)(part1 | part2);
                    value ^= -431219573 ^ salt;
                    return value;
                }
               case 161:
                {
                    // XorMultipleRotateInstruction
                    value ^= 1658933717 ^ salt;
                    value = value * 642349663 + _secretKey[198];
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 162:
                {
                    // AddRotateXorInstruction
                    value += -1278798944 + _secretKey[72];
                    uint part1 = (uint)value << 20;
                    uint part2 = (uint)value >> (32 - 20);
                    value = (int)(part1 | part2);
                    value ^= 609336148 ^ salt;
                    return value;
                }
               case 163:
                {
                    // MultipleXorRotateInstruction
                    value = value * -1217570675 + _secretKey[57];
                    value ^= -1055021038 ^ salt;
                    uint part1 = (uint)value << 26;
                    uint part2 = (uint)value >> (32 - 26);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 164:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1129428085 + _secretKey[225];
                    value ^= 1389308323 ^ salt;
                    uint part1 = (uint)value << 7;
                    uint part2 = (uint)value >> (32 - 7);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 165:
                {
                    // XorAddRotateInstruction
                    value ^= 318043677 ^ salt;
                    value += -1939584600 + _secretKey[124];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 166:
                {
                    // MultipleXorRotateInstruction
                    value = value * 601748357 + _secretKey[184];
                    value ^= 2047590880 ^ salt;
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 167:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[114]) + salt) ^ -312123044;
                    return value;
                }
               case 168:
                {
                    // AddXorRotateInstruction
                    value += 1897551751 + _secretKey[139];
                    value ^= -1299860280 ^ salt;
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 169:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = ((int)(part1 | part2) ^ _secretKey[10]) + salt;
                    return value;
                }
               case 170:
                {
                    // MultipleInstruction
                    value = value *  -1716044921 + _secretKey[60] + salt;
                    return value;
                }
               case 171:
                {
                    // XorMultipleRotateInstruction
                    value ^= 820953326 ^ salt;
                    value = value * 1295924473 + _secretKey[242];
                    uint part1 = (uint)value << 21;
                    uint part2 = (uint)value >> (32 - 21);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 172:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[89]) + salt) ^ 921116076;
                    return value;
                }
               case 173:
                {
                    // AddInstruction
                    value = ((value + _secretKey[82]) ^ salt) + -1261901861;
                    return value;
                }
               case 174:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1029416329 + _secretKey[42];
                    uint part1 = (uint)value << 1;
                    uint part2 = (uint)value >> (32 - 1);
                    value = (int)(part1 | part2);
                    value ^= -1758870671 ^ salt;
                    return value;
                }
               case 175:
                {
                    // XorAddRotateInstruction
                    value ^= -99371457 ^ salt;
                    value += 1254595032 + _secretKey[10];
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 176:
                {
                    // AddXorRotateInstruction
                    value += 1901168605 + _secretKey[131];
                    value ^= -2036462975 ^ salt;
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 177:
                {
                    // MultipleRotateXorInstruction
                    value = value * 383500913 + _secretKey[29];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    value ^= -805817000 ^ salt;
                    return value;
                }
               case 178:
                {
                    // AddXorRotateInstruction
                    value += 28490730 + _secretKey[13];
                    value ^= 762578411 ^ salt;
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 179:
                {
                    // XorAddRotateInstruction
                    value ^= 1812019570 ^ salt;
                    value += -41308497 + _secretKey[85];
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 180:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[231]) + salt) ^ 1563652208;
                    return value;
                }
               case 181:
                {
                    // AddRotateXorInstruction
                    value += -542613261 + _secretKey[75];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    value ^= -1148350591 ^ salt;
                    return value;
                }
               case 182:
                {
                    // AddXorRotateInstruction
                    value += 1462569147 + _secretKey[135];
                    value ^= 1842755263 ^ salt;
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 183:
                {
                    // MultipleXorRotateInstruction
                    value = value * 934570325 + _secretKey[15];
                    value ^= -159244912 ^ salt;
                    uint part1 = (uint)value << 8;
                    uint part2 = (uint)value >> (32 - 8);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 184:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = ((int)(part1 | part2) ^ _secretKey[230]) + salt;
                    return value;
                }
               case 185:
                {
                    // AddRotateXorInstruction
                    value += 45305078 + _secretKey[119];
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = (int)(part1 | part2);
                    value ^= 1218428368 ^ salt;
                    return value;
                }
               case 186:
                {
                    // AddXorRotateInstruction
                    value += 148564506 + _secretKey[160];
                    value ^= 567100030 ^ salt;
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 187:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 21;
                    uint part2 = (uint)value >> (32 - 21);
                    value = ((int)(part1 | part2) ^ _secretKey[242]) + salt;
                    return value;
                }
               case 188:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1881635163 + _secretKey[30];
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= 730302816 ^ salt;
                    return value;
                }
               case 189:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = ((int)(part1 | part2) ^ _secretKey[93]) + salt;
                    return value;
                }
               case 190:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = ((int)(part1 | part2) ^ _secretKey[69]) + salt;
                    return value;
                }
               case 191:
                {
                    // XorMultipleRotateInstruction
                    value ^= 1474104403 ^ salt;
                    value = value * 1888537457 + _secretKey[153];
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 192:
                {
                    // AddInstruction
                    value = ((value + _secretKey[170]) ^ salt) + 1345231273;
                    return value;
                }
               case 193:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = ((int)(part1 | part2) ^ _secretKey[138]) + salt;
                    return value;
                }
               case 194:
                {
                    // AddRotateXorInstruction
                    value += -1649692985 + _secretKey[138];
                    uint part1 = (uint)value << 2;
                    uint part2 = (uint)value >> (32 - 2);
                    value = (int)(part1 | part2);
                    value ^= -1086752221 ^ salt;
                    return value;
                }
               case 195:
                {
                    // XorMultipleRotateInstruction
                    value ^= 216360478 ^ salt;
                    value = value * 198928957 + _secretKey[23];
                    uint part1 = (uint)value << 5;
                    uint part2 = (uint)value >> (32 - 5);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 196:
                {
                    // XorAddRotateInstruction
                    value ^= 1736125070 ^ salt;
                    value += -922639548 + _secretKey[211];
                    uint part1 = (uint)value << 25;
                    uint part2 = (uint)value >> (32 - 25);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 197:
                {
                    // XorAddRotateInstruction
                    value ^= -1590872932 ^ salt;
                    value += -1729078426 + _secretKey[124];
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 198:
                {
                    // AddXorRotateInstruction
                    value += -2086916257 + _secretKey[135];
                    value ^= 2101329043 ^ salt;
                    uint part1 = (uint)value << 13;
                    uint part2 = (uint)value >> (32 - 13);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 199:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[133]) + salt) ^ -1188487898;
                    return value;
                }
               case 200:
                {
                    // MultipleInstruction
                    value = value *  -165216181 + _secretKey[162] + salt;
                    return value;
                }
               case 201:
                {
                    // XorAddRotateInstruction
                    value ^= 2087683186 ^ salt;
                    value += -1882888353 + _secretKey[153];
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 202:
                {
                    // MultipleRotateXorInstruction
                    value = value * -1941291837 + _secretKey[58];
                    uint part1 = (uint)value << 22;
                    uint part2 = (uint)value >> (32 - 22);
                    value = (int)(part1 | part2);
                    value ^= -1855365205 ^ salt;
                    return value;
                }
               case 203:
                {
                    // MultipleInstruction
                    value = value *  2036569383 + _secretKey[66] + salt;
                    return value;
                }
               case 204:
                {
                    // MultipleXorRotateInstruction
                    value = value * 795577849 + _secretKey[206];
                    value ^= 1668989123 ^ salt;
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 205:
                {
                    // MultipleInstruction
                    value = value *  -1063887357 + _secretKey[169] + salt;
                    return value;
                }
               case 206:
                {
                    // XorMultipleRotateInstruction
                    value ^= -387621173 ^ salt;
                    value = value * 413706907 + _secretKey[143];
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 207:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[133]) + salt) ^ -1302837102;
                    return value;
                }
               case 208:
                {
                    // XorMultipleRotateInstruction
                    value ^= 1201861103 ^ salt;
                    value = value * -1349002009 + _secretKey[5];
                    uint part1 = (uint)value << 29;
                    uint part2 = (uint)value >> (32 - 29);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 209:
                {
                    // AddInstruction
                    value = ((value + _secretKey[188]) ^ salt) + -1698116194;
                    return value;
                }
               case 210:
                {
                    // XorAddRotateInstruction
                    value ^= 955827838 ^ salt;
                    value += -5412811 + _secretKey[40];
                    uint part1 = (uint)value << 3;
                    uint part2 = (uint)value >> (32 - 3);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 211:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1209501053 + _secretKey[206];
                    value ^= -261186202 ^ salt;
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 212:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[215]) + salt) ^ 1451245279;
                    return value;
                }
               case 213:
                {
                    // AddInstruction
                    value = ((value + _secretKey[248]) ^ salt) + -48271475;
                    return value;
                }
               case 214:
                {
                    // MultipleRotateXorInstruction
                    value = value * -685299407 + _secretKey[72];
                    uint part1 = (uint)value << 31;
                    uint part2 = (uint)value >> (32 - 31);
                    value = (int)(part1 | part2);
                    value ^= 280704379 ^ salt;
                    return value;
                }
               case 215:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1188587057 + _secretKey[176];
                    uint part1 = (uint)value << 17;
                    uint part2 = (uint)value >> (32 - 17);
                    value = (int)(part1 | part2);
                    value ^= -1507466225 ^ salt;
                    return value;
                }
               case 216:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = ((int)(part1 | part2) ^ _secretKey[162]) + salt;
                    return value;
                }
               case 217:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[108]) + salt) ^ -1329546797;
                    return value;
                }
               case 218:
                {
                    // XorAddRotateInstruction
                    value ^= 846489904 ^ salt;
                    value += 1710889501 + _secretKey[85];
                    uint part1 = (uint)value << 11;
                    uint part2 = (uint)value >> (32 - 11);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 219:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[127]) + salt) ^ -339712479;
                    return value;
                }
               case 220:
                {
                    // XorAddRotateInstruction
                    value ^= -1008587035 ^ salt;
                    value += -308188673 + _secretKey[78];
                    uint part1 = (uint)value << 15;
                    uint part2 = (uint)value >> (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 221:
                {
                    // MultipleInstruction
                    value = value *  -2016434293 + _secretKey[111] + salt;
                    return value;
                }
               case 222:
                {
                    // MultipleInstruction
                    value = value *  -491329185 + _secretKey[198] + salt;
                    return value;
                }
               case 223:
                {
                    // AddInstruction
                    value = ((value + _secretKey[160]) ^ salt) + -449129672;
                    return value;
                }
               case 224:
                {
                    // MultipleXorRotateInstruction
                    value = value * -86469931 + _secretKey[84];
                    value ^= -180027834 ^ salt;
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 225:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[58]) + salt) ^ 946019090;
                    return value;
                }
               case 226:
                {
                    // AddRotateXorInstruction
                    value += 694016884 + _secretKey[225];
                    uint part1 = (uint)value << 3;
                    uint part2 = (uint)value >> (32 - 3);
                    value = (int)(part1 | part2);
                    value ^= 1350981383 ^ salt;
                    return value;
                }
               case 227:
                {
                    // AddRotateXorInstruction
                    value += -870643939 + _secretKey[168];
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = (int)(part1 | part2);
                    value ^= 1680252929 ^ salt;
                    return value;
                }
               case 228:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 4;
                    uint part2 = (uint)value >> (32 - 4);
                    value = ((int)(part1 | part2) ^ _secretKey[184]) + salt;
                    return value;
                }
               case 229:
                {
                    // AddRotateXorInstruction
                    value += 1785715822 + _secretKey[199];
                    uint part1 = (uint)value << 28;
                    uint part2 = (uint)value >> (32 - 28);
                    value = (int)(part1 | part2);
                    value ^= -1727043214 ^ salt;
                    return value;
                }
               case 230:
                {
                    // XorInstruction
                    value = ((value ^ _secretKey[139]) + salt) ^ -1277148537;
                    return value;
                }
               case 231:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1298248033 + _secretKey[226];
                    value ^= 1940873679 ^ salt;
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 232:
                {
                    // XorMultipleRotateInstruction
                    value ^= -879839609 ^ salt;
                    value = value * 1286764861 + _secretKey[171];
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 233:
                {
                    // AddInstruction
                    value = ((value + _secretKey[181]) ^ salt) + 328489970;
                    return value;
                }
               case 234:
                {
                    // MultipleInstruction
                    value = value *  -1393808723 + _secretKey[89] + salt;
                    return value;
                }
               case 235:
                {
                    // XorAddRotateInstruction
                    value ^= 1290000091 ^ salt;
                    value += -1977097134 + _secretKey[203];
                    uint part1 = (uint)value << 8;
                    uint part2 = (uint)value >> (32 - 8);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 236:
                {
                    // AddInstruction
                    value = ((value + _secretKey[113]) ^ salt) + 1890859361;
                    return value;
                }
               case 237:
                {
                    // AddRotateXorInstruction
                    value += 1045620543 + _secretKey[216];
                    uint part1 = (uint)value << 10;
                    uint part2 = (uint)value >> (32 - 10);
                    value = (int)(part1 | part2);
                    value ^= 1434413518 ^ salt;
                    return value;
                }
               case 238:
                {
                    // AddXorRotateInstruction
                    value += -1706485027 + _secretKey[131];
                    value ^= 1591345537 ^ salt;
                    uint part1 = (uint)value << 0;
                    uint part2 = (uint)value >> (32 - 0);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 239:
                {
                    // AddXorRotateInstruction
                    value += 1271081841 + _secretKey[29];
                    value ^= 1117669949 ^ salt;
                    uint part1 = (uint)value << 24;
                    uint part2 = (uint)value >> (32 - 24);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 240:
                {
                    // XorMultipleRotateInstruction
                    value ^= -842525462 ^ salt;
                    value = value * 1426591501 + _secretKey[235];
                    uint part1 = (uint)value << 12;
                    uint part2 = (uint)value >> (32 - 12);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 241:
                {
                    // AddInstruction
                    value = ((value + _secretKey[175]) ^ salt) + 1030822002;
                    return value;
                }
               case 242:
                {
                    // MultipleRotateXorInstruction
                    value = value * 1176352505 + _secretKey[57];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 811922151 ^ salt;
                    return value;
                }
               case 243:
                {
                    // AddRotateXorInstruction
                    value += 656680947 + _secretKey[75];
                    uint part1 = (uint)value << 23;
                    uint part2 = (uint)value >> (32 - 23);
                    value = (int)(part1 | part2);
                    value ^= -1258702719 ^ salt;
                    return value;
                }
               case 244:
                {
                    // XorMultipleRotateInstruction
                    value ^= 90809787 ^ salt;
                    value = value * -1741148537 + _secretKey[191];
                    uint part1 = (uint)value << 19;
                    uint part2 = (uint)value >> (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 245:
                {
                    // AddRotateXorInstruction
                    value += -532913580 + _secretKey[15];
                    uint part1 = (uint)value << 16;
                    uint part2 = (uint)value >> (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 99436168 ^ salt;
                    return value;
                }
               case 246:
                {
                    // MultipleXorRotateInstruction
                    value = value * 1306804229 + _secretKey[230];
                    value ^= 1471598712 ^ salt;
                    uint part1 = (uint)value << 22;
                    uint part2 = (uint)value >> (32 - 22);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 247:
                {
                    // XorMultipleRotateInstruction
                    value ^= 1941306053 ^ salt;
                    value = value * -939631919 + _secretKey[15];
                    uint part1 = (uint)value << 26;
                    uint part2 = (uint)value >> (32 - 26);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 248:
                {
                    // AddRotateXorInstruction
                    value += 883137918 + _secretKey[96];
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = (int)(part1 | part2);
                    value ^= 2045091157 ^ salt;
                    return value;
                }
               case 249:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 13;
                    uint part2 = (uint)value >> (32 - 13);
                    value = ((int)(part1 | part2) ^ _secretKey[165]) + salt;
                    return value;
                }
               case 250:
                {
                    // AddInstruction
                    value = ((value + _secretKey[96]) ^ salt) + -394947456;
                    return value;
                }
               case 251:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 18;
                    uint part2 = (uint)value >> (32 - 18);
                    value = ((int)(part1 | part2) ^ _secretKey[93]) + salt;
                    return value;
                }
               case 252:
                {
                    // AddInstruction
                    value = ((value + _secretKey[69]) ^ salt) + 1917332797;
                    return value;
                }
               case 253:
                {
                    // AddXorRotateInstruction
                    value += 1006809939 + _secretKey[113];
                    value ^= -1509317223 ^ salt;
                    uint part1 = (uint)value << 14;
                    uint part2 = (uint)value >> (32 - 14);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 254:
                {
                    // BitRotateInstruction
                    uint part1 = (uint)value << 9;
                    uint part2 = (uint)value >> (32 - 9);
                    value = ((int)(part1 | part2) ^ _secretKey[170]) + salt;
                    return value;
                }
               case 255:
                {
                    // AddInstruction
                    value = ((value + _secretKey[138]) ^ salt) + 683715132;
                    return value;
                }

                default:
                    throw new System.Exception($"Invalid opCode:{opCode}");
            }
        }

        private int ExecuteDecrypt(int value, int opCode, int salt)
        {
            switch (opCode)
            {
               case 0:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[84] - salt) * -1954824987;
                    return value;
                }
               case 1:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1817406469 ^ salt;
                    uint value2 = (uint)value >> 4;
                    uint part1 = (uint)value << (32 - 4);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[136]) * -2114748303;
                    return value;
                }
               case 2:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 5;
                    uint part1 = (uint)value << (32 - 5);
                    value = (int)(value2 | part1);
                    value ^= -1498541961 ^ salt;
                    value = (value - _secretKey[246]) * -203485751;
                    return value;
                }
               case 3:
                {
                    // AddRotateXorInstruction
                    value ^= 29411710 ^ salt;
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value -= -1207833585 + _secretKey[26];
                    return value;
                }
               case 4:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[85]);
                    uint part1 = value2 >> 18;
                    uint part2 = value2 << (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 5:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value ^= 86149918 ^ salt;
                    value = (value - _secretKey[165]) * -327424699;
                    return value;
                }
               case 6:
                {
                    // AddRotateXorInstruction
                    value ^= -1262500500 ^ salt;
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value -= -1856354856 + _secretKey[178];
                    return value;
                }
               case 7:
                {
                    // XorInstruction
                    value = ((value ^ 665464645) - salt) ^ _secretKey[53];
                    return value;
                }
               case 8:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[206]) * -1476140375;
                    value ^= -1044567439 ^ salt;
                    return value;
                }
               case 9:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[196] - salt) * 2125307395;
                    return value;
                }
               case 10:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 10;
                    uint part1 = (uint)value << (32 - 10);
                    value = (int)(value2 | part1);
                    value -= -1035239660 + _secretKey[199];
                    value ^= -755609206 ^ salt;
                    return value;
                }
               case 11:
                {
                    // AddInstruction
                    value = ((value  - -1177184477) ^ salt) - _secretKey[89];
                    return value;
                }
               case 12:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[23]);
                    uint part1 = value2 >> 29;
                    uint part2 = value2 << (32 - 29);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 13:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 19;
                    uint part1 = (uint)value << (32 - 19);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[68]) * 1170138479;
                    value ^= -1510419150 ^ salt;
                    return value;
                }
               case 14:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1134639492 ^ salt;
                    uint value2 = (uint)value >> 6;
                    uint part1 = (uint)value << (32 - 6);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[156]) * 275824265;
                    return value;
                }
               case 15:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[95]);
                    uint part1 = value2 >> 7;
                    uint part2 = value2 << (32 - 7);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 16:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 6;
                    uint part1 = (uint)value << (32 - 6);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[79]) * 1552472901;
                    value ^= -759315565 ^ salt;
                    return value;
                }
               case 17:
                {
                    // MultipleRotateXorInstruction
                    value ^= -3885258 ^ salt;
                    uint value2 = (uint)value >> 2;
                    uint part1 = (uint)value << (32 - 2);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[75]) * -1943909725;
                    return value;
                }
               case 18:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 9;
                    uint part1 = (uint)value << (32 - 9);
                    value = (int)(value2 | part1);
                    value ^= 1207613963 ^ salt;
                    value = (value - _secretKey[153]) * 1985974175;
                    return value;
                }
               case 19:
                {
                    // MultipleRotateXorInstruction
                    value ^= 792769043 ^ salt;
                    uint value2 = (uint)value >> 11;
                    uint part1 = (uint)value << (32 - 11);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[86]) * 1412922099;
                    return value;
                }
               case 20:
                {
                    // AddInstruction
                    value = ((value  - -512520382) ^ salt) - _secretKey[194];
                    return value;
                }
               case 21:
                {
                    // XorInstruction
                    value = ((value ^ -1864951858) - salt) ^ _secretKey[195];
                    return value;
                }
               case 22:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[2]);
                    uint part1 = value2 >> 15;
                    uint part2 = value2 << (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 23:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[203] - salt) * 891162519;
                    return value;
                }
               case 24:
                {
                    // AddInstruction
                    value = ((value  - 2026667919) ^ salt) - _secretKey[110];
                    return value;
                }
               case 25:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 15;
                    uint part2 = (uint)value << (32 - 15);
                    value = (int)(part1 | part2);
                    value ^= 392708821 ^ salt;
                    value -= 848657810 + _secretKey[133];
                    return value;
                }
               case 26:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[93]);
                    uint part1 = value2 >> 5;
                    uint part2 = value2 << (32 - 5);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 27:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 30;
                    uint part1 = (uint)value << (32 - 30);
                    value = (int)(value2 | part1);
                    value -= 1284012732 + _secretKey[58];
                    value ^= 1294662302 ^ salt;
                    return value;
                }
               case 28:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[138]) * -708470805;
                    value ^= -1438081752 ^ salt;
                    return value;
                }
               case 29:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[240]);
                    uint part1 = value2 >> 6;
                    uint part2 = value2 << (32 - 6);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 30:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1594594445 ^ salt;
                    uint value2 = (uint)value >> 24;
                    uint part1 = (uint)value << (32 - 24);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[215]) * 1857241631;
                    return value;
                }
               case 31:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[49]);
                    uint part1 = value2 >> 19;
                    uint part2 = value2 << (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 32:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 17;
                    uint part1 = (uint)value << (32 - 17);
                    value = (int)(value2 | part1);
                    value -= -1038657413 + _secretKey[247];
                    value ^= 226193183 ^ salt;
                    return value;
                }
               case 33:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value -= 459902223 + _secretKey[252];
                    value ^= -484591087 ^ salt;
                    return value;
                }
               case 34:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value ^= 1186351980 ^ salt;
                    value = (value - _secretKey[211]) * 1159592341;
                    return value;
                }
               case 35:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 15;
                    uint part1 = (uint)value << (32 - 15);
                    value = (int)(value2 | part1);
                    value -= -283413931 + _secretKey[235];
                    value ^= 1658142493 ^ salt;
                    return value;
                }
               case 36:
                {
                    // XorInstruction
                    value = ((value ^ -1892941953) - salt) ^ _secretKey[90];
                    return value;
                }
               case 37:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 31;
                    uint part1 = (uint)value << (32 - 31);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[111]) * -674216273;
                    value ^= -1297440001 ^ salt;
                    return value;
                }
               case 38:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1353158598 ^ salt;
                    uint value2 = (uint)value >> 31;
                    uint part1 = (uint)value << (32 - 31);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[213]) * 803536783;
                    return value;
                }
               case 39:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 20;
                    uint part1 = (uint)value << (32 - 20);
                    value = (int)(value2 | part1);
                    value -= 873171360 + _secretKey[72];
                    value ^= -934836680 ^ salt;
                    return value;
                }
               case 40:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 18;
                    uint part1 = (uint)value << (32 - 18);
                    value = (int)(value2 | part1);
                    value ^= -28088263 ^ salt;
                    value = (value - _secretKey[140]) * 1881436791;
                    return value;
                }
               case 41:
                {
                    // AddRotateXorInstruction
                    value ^= -312111197 ^ salt;
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value -= 969234286 + _secretKey[116];
                    return value;
                }
               case 42:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[29] - salt) * 2102789665;
                    return value;
                }
               case 43:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[1]);
                    uint part1 = value2 >> 28;
                    uint part2 = value2 << (32 - 28);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 44:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[184]);
                    uint part1 = value2 >> 4;
                    uint part2 = value2 << (32 - 4);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 45:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 18;
                    uint part1 = (uint)value << (32 - 18);
                    value = (int)(value2 | part1);
                    value ^= 1068062556 ^ salt;
                    value = (value - _secretKey[199]) * 85627791;
                    return value;
                }
               case 46:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[200]) * 1138735395;
                    value ^= 196245895 ^ salt;
                    return value;
                }
               case 47:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 7;
                    uint part1 = (uint)value << (32 - 7);
                    value = (int)(value2 | part1);
                    value ^= -1964107221 ^ salt;
                    value = (value - _secretKey[10]) * -1413533649;
                    return value;
                }
               case 48:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 18;
                    uint part1 = (uint)value << (32 - 18);
                    value = (int)(value2 | part1);
                    value ^= -38668552 ^ salt;
                    value = (value - _secretKey[238]) * 665251331;
                    return value;
                }
               case 49:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[172] - salt) * -818173423;
                    return value;
                }
               case 50:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 11;
                    uint part2 = (uint)value << (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= -2098495662 ^ salt;
                    value -= 1412414820 + _secretKey[219];
                    return value;
                }
               case 51:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[97]);
                    uint part1 = value2 >> 10;
                    uint part2 = value2 << (32 - 10);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 52:
                {
                    // MultipleRotateXorInstruction
                    value ^= -21948406 ^ salt;
                    uint value2 = (uint)value >> 24;
                    uint part1 = (uint)value << (32 - 24);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[63]) * -99761833;
                    return value;
                }
               case 53:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[221]);
                    uint part1 = value2 >> 7;
                    uint part2 = value2 << (32 - 7);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 54:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[96] - salt) * -2034360447;
                    return value;
                }
               case 55:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1022882984 ^ salt;
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[29]) * -899658607;
                    return value;
                }
               case 56:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 12;
                    uint part1 = (uint)value << (32 - 12);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[235]) * 340919493;
                    value ^= 2122077674 ^ salt;
                    return value;
                }
               case 57:
                {
                    // AddRotateXorInstruction
                    value ^= -720824840 ^ salt;
                    uint value2 = (uint)value >> 21;
                    uint part1 = (uint)value << (32 - 21);
                    value = (int)(value2 | part1);
                    value -= -503448718 + _secretKey[175];
                    return value;
                }
               case 58:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[231] - salt) * 913639057;
                    return value;
                }
               case 59:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[75]);
                    uint part1 = value2 >> 19;
                    uint part2 = value2 << (32 - 19);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 60:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1420744071 ^ salt;
                    uint value2 = (uint)value >> 27;
                    uint part1 = (uint)value << (32 - 27);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[171]) * 238093953;
                    return value;
                }
               case 61:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 15;
                    uint part1 = (uint)value << (32 - 15);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[84]) * -2096065051;
                    value ^= 1366253139 ^ salt;
                    return value;
                }
               case 62:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 6;
                    uint part1 = (uint)value << (32 - 6);
                    value = (int)(value2 | part1);
                    value ^= -1974823163 ^ salt;
                    value = (value - _secretKey[132]) * 303900345;
                    return value;
                }
               case 63:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[119]);
                    uint part1 = value2 >> 22;
                    uint part2 = value2 << (32 - 22);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 64:
                {
                    // XorInstruction
                    value = ((value ^ 1262347216) - salt) ^ _secretKey[15];
                    return value;
                }
               case 65:
                {
                    // AddInstruction
                    value = ((value  - 1780280992) ^ salt) - _secretKey[126];
                    return value;
                }
               case 66:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 13;
                    uint part1 = (uint)value << (32 - 13);
                    value = (int)(value2 | part1);
                    value ^= -1198162446 ^ salt;
                    value = (value - _secretKey[85]) * 1314512283;
                    return value;
                }
               case 67:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[128] - salt) * 463417823;
                    return value;
                }
               case 68:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[178]);
                    uint part1 = value2 >> 24;
                    uint part2 = value2 << (32 - 24);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 69:
                {
                    // XorInstruction
                    value = ((value ^ 1649427052) - salt) ^ _secretKey[61];
                    return value;
                }
               case 70:
                {
                    // MultipleRotateXorInstruction
                    value ^= 849172121 ^ salt;
                    uint value2 = (uint)value >> 17;
                    uint part1 = (uint)value << (32 - 17);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[83]) * 368180765;
                    return value;
                }
               case 71:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[169]);
                    uint part1 = value2 >> 16;
                    uint part2 = value2 << (32 - 16);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 72:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 20;
                    uint part1 = (uint)value << (32 - 20);
                    value = (int)(value2 | part1);
                    value -= -2092062916 + _secretKey[138];
                    value ^= 1068287172 ^ salt;
                    return value;
                }
               case 73:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[34] - salt) * -495145181;
                    return value;
                }
               case 74:
                {
                    // MultipleRotateXorInstruction
                    value ^= -145743337 ^ salt;
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[30]) * 1735064809;
                    return value;
                }
               case 75:
                {
                    // MultipleRotateXorInstruction
                    value ^= 280941267 ^ salt;
                    uint value2 = (uint)value >> 4;
                    uint part1 = (uint)value << (32 - 4);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[142]) * 442035963;
                    return value;
                }
               case 76:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[156] - salt) * -971441783;
                    return value;
                }
               case 77:
                {
                    // AddRotateXorInstruction
                    value ^= 827331935 ^ salt;
                    uint value2 = (uint)value >> 7;
                    uint part1 = (uint)value << (32 - 7);
                    value = (int)(value2 | part1);
                    value -= 314625916 + _secretKey[192];
                    return value;
                }
               case 78:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[141] - salt) * -1649657957;
                    return value;
                }
               case 79:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 11;
                    uint part2 = (uint)value << (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= -11549173 ^ salt;
                    value -= 1607953190 + _secretKey[133];
                    return value;
                }
               case 80:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 25;
                    uint part1 = (uint)value << (32 - 25);
                    value = (int)(value2 | part1);
                    value -= 687186546 + _secretKey[95];
                    value ^= -1454482890 ^ salt;
                    return value;
                }
               case 81:
                {
                    // XorInstruction
                    value = ((value ^ 814860713) - salt) ^ _secretKey[195];
                    return value;
                }
               case 82:
                {
                    // AddRotateXorInstruction
                    value ^= 213310246 ^ salt;
                    uint value2 = (uint)value >> 19;
                    uint part1 = (uint)value << (32 - 19);
                    value = (int)(value2 | part1);
                    value -= 1998643542 + _secretKey[171];
                    return value;
                }
               case 83:
                {
                    // AddRotateXorInstruction
                    value ^= -696314173 ^ salt;
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value -= 542686146 + _secretKey[249];
                    return value;
                }
               case 84:
                {
                    // AddRotateXorInstruction
                    value ^= 118718247 ^ salt;
                    uint value2 = (uint)value >> 9;
                    uint part1 = (uint)value << (32 - 9);
                    value = (int)(value2 | part1);
                    value -= 1734820207 + _secretKey[2];
                    return value;
                }
               case 85:
                {
                    // XorInstruction
                    value = ((value ^ 1553710234) - salt) ^ _secretKey[143];
                    return value;
                }
               case 86:
                {
                    // AddRotateXorInstruction
                    value ^= -1402843691 ^ salt;
                    uint value2 = (uint)value >> 5;
                    uint part1 = (uint)value << (32 - 5);
                    value = (int)(value2 | part1);
                    value -= -217984331 + _secretKey[146];
                    return value;
                }
               case 87:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[5] - salt) * -814971689;
                    return value;
                }
               case 88:
                {
                    // MultipleRotateXorInstruction
                    value ^= -2044505542 ^ salt;
                    uint value2 = (uint)value >> 28;
                    uint part1 = (uint)value << (32 - 28);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[158]) * 681320217;
                    return value;
                }
               case 89:
                {
                    // AddInstruction
                    value = ((value  - -1213654475) ^ salt) - _secretKey[40];
                    return value;
                }
               case 90:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 6;
                    uint part1 = (uint)value << (32 - 6);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[206]) * 583163349;
                    value ^= -1886972278 ^ salt;
                    return value;
                }
               case 91:
                {
                    // AddRotateXorInstruction
                    value ^= -979249928 ^ salt;
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value -= -1724625239 + _secretKey[223];
                    return value;
                }
               case 92:
                {
                    // XorInstruction
                    value = ((value ^ -1104541704) - salt) ^ _secretKey[83];
                    return value;
                }
               case 93:
                {
                    // XorInstruction
                    value = ((value ^ 311150152) - salt) ^ _secretKey[31];
                    return value;
                }
               case 94:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 17;
                    uint part1 = (uint)value << (32 - 17);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[176]) * 1535026385;
                    value ^= 116496631 ^ salt;
                    return value;
                }
               case 95:
                {
                    // MultipleRotateXorInstruction
                    value ^= 2133438141 ^ salt;
                    uint value2 = (uint)value >> 2;
                    uint part1 = (uint)value << (32 - 2);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[87]) * 1779203413;
                    return value;
                }
               case 96:
                {
                    // MultipleRotateXorInstruction
                    value ^= 225535005 ^ salt;
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[174]) * 521638757;
                    return value;
                }
               case 97:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1703839105 ^ salt;
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[175]) * -1131776573;
                    return value;
                }
               case 98:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 15;
                    uint part1 = (uint)value << (32 - 15);
                    value = (int)(value2 | part1);
                    value -= -1783079937 + _secretKey[78];
                    value ^= -447564571 ^ salt;
                    return value;
                }
               case 99:
                {
                    // XorInstruction
                    value = ((value ^ -316631669) - salt) ^ _secretKey[111];
                    return value;
                }
               case 100:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 24;
                    uint part1 = (uint)value << (32 - 24);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[252]) * 898292471;
                    value ^= -405694625 ^ salt;
                    return value;
                }
               case 101:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 6;
                    uint part1 = (uint)value << (32 - 6);
                    value = (int)(value2 | part1);
                    value ^= -1551058348 ^ salt;
                    value = (value - _secretKey[212]) * -22167815;
                    return value;
                }
               case 102:
                {
                    // AddInstruction
                    value = ((value  - -853736135) ^ salt) - _secretKey[18];
                    return value;
                }
               case 103:
                {
                    // AddInstruction
                    value = ((value  - 1321376878) ^ salt) - _secretKey[116];
                    return value;
                }
               case 104:
                {
                    // MultipleRotateXorInstruction
                    value ^= 541697309 ^ salt;
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[7]) * -1964889845;
                    return value;
                }
               case 105:
                {
                    // AddRotateXorInstruction
                    value ^= 1176608900 ^ salt;
                    uint value2 = (uint)value >> 4;
                    uint part1 = (uint)value << (32 - 4);
                    value = (int)(value2 | part1);
                    value -= -822594180 + _secretKey[1];
                    return value;
                }
               case 106:
                {
                    // AddInstruction
                    value = ((value  - -882893600) ^ salt) - _secretKey[110];
                    return value;
                }
               case 107:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1566546809 ^ salt;
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[114]) * 406480373;
                    return value;
                }
               case 108:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[97] - salt) * 200608377;
                    return value;
                }
               case 109:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 7;
                    uint part1 = (uint)value << (32 - 7);
                    value = (int)(value2 | part1);
                    value -= 1161425930 + _secretKey[43];
                    value ^= -72794161 ^ salt;
                    return value;
                }
               case 110:
                {
                    // AddInstruction
                    value = ((value  - -1619543125) ^ salt) - _secretKey[238];
                    return value;
                }
               case 111:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 12;
                    uint part1 = (uint)value << (32 - 12);
                    value = (int)(value2 | part1);
                    value -= -1766468683 + _secretKey[241];
                    value ^= 1170459122 ^ salt;
                    return value;
                }
               case 112:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 11;
                    uint part2 = (uint)value << (32 - 11);
                    value = (int)(part1 | part2);
                    value ^= 323019346 ^ salt;
                    value -= 251687012 + _secretKey[219];
                    return value;
                }
               case 113:
                {
                    // AddInstruction
                    value = ((value  - -1049972438) ^ salt) - _secretKey[97];
                    return value;
                }
               case 114:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 10;
                    uint part1 = (uint)value << (32 - 10);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[216]) * 594045631;
                    value ^= -492239002 ^ salt;
                    return value;
                }
               case 115:
                {
                    // AddInstruction
                    value = ((value  - -748126329) ^ salt) - _secretKey[221];
                    return value;
                }
               case 116:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 17;
                    uint part2 = (uint)value << (32 - 17);
                    value = (int)(part1 | part2);
                    value ^= -5933889 ^ salt;
                    value -= -441051263 + _secretKey[96];
                    return value;
                }
               case 117:
                {
                    // MultipleRotateXorInstruction
                    value ^= 543895274 ^ salt;
                    uint value2 = (uint)value >> 11;
                    uint part1 = (uint)value << (32 - 11);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[88]) * 1253478165;
                    return value;
                }
               case 118:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[140] - salt) * 1897067971;
                    return value;
                }
               case 119:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[175]);
                    uint part1 = value2 >> 18;
                    uint part2 = value2 << (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 120:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1300544743 ^ salt;
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[57]) * -927555255;
                    return value;
                }
               case 121:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value -= 678597707 + _secretKey[119];
                    value ^= -547380749 ^ salt;
                    return value;
                }
               case 122:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 19;
                    uint part1 = (uint)value << (32 - 19);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[191]) * -1394333385;
                    value ^= 683601851 ^ salt;
                    return value;
                }
               case 123:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[15]);
                    uint part1 = value2 >> 20;
                    uint part2 = value2 << (32 - 20);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 124:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[132]);
                    uint part1 = value2 >> 8;
                    uint part2 = value2 << (32 - 8);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 125:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[120] - salt) * 412809175;
                    return value;
                }
               case 126:
                {
                    // AddInstruction
                    value = ((value  - -1374139785) ^ salt) - _secretKey[197];
                    return value;
                }
               case 127:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[26]);
                    uint part1 = value2 >> 15;
                    uint part2 = value2 << (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 128:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 21;
                    uint part1 = (uint)value << (32 - 21);
                    value = (int)(value2 | part1);
                    value -= 723646816 + _secretKey[146];
                    value ^= 1415280510 ^ salt;
                    return value;
                }
               case 129:
                {
                    // AddRotateXorInstruction
                    value ^= 2132310656 ^ salt;
                    uint value2 = (uint)value >> 30;
                    uint part1 = (uint)value << (32 - 30);
                    value = (int)(value2 | part1);
                    value -= 1410706317 + _secretKey[165];
                    return value;
                }
               case 130:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 12;
                    uint part1 = (uint)value << (32 - 12);
                    value = (int)(value2 | part1);
                    value -= -483221582 + _secretKey[93];
                    value ^= 1264971736 ^ salt;
                    return value;
                }
               case 131:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 17;
                    uint part2 = (uint)value << (32 - 17);
                    value = (int)(part1 | part2);
                    value ^= 1531807059 ^ salt;
                    value -= -1872731835 + _secretKey[53];
                    return value;
                }
               case 132:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 10;
                    uint part2 = (uint)value << (32 - 10);
                    value = (int)(part1 | part2);
                    value ^= 306666665 ^ salt;
                    value -= 264114638 + _secretKey[240];
                    return value;
                }
               case 133:
                {
                    // AddRotateXorInstruction
                    value ^= -1266414649 ^ salt;
                    uint value2 = (uint)value >> 20;
                    uint part1 = (uint)value << (32 - 20);
                    value = (int)(value2 | part1);
                    value -= 824712252 + _secretKey[138];
                    return value;
                }
               case 134:
                {
                    // AddRotateXorInstruction
                    value ^= 2133737246 ^ salt;
                    uint value2 = (uint)value >> 25;
                    uint part1 = (uint)value << (32 - 25);
                    value = (int)(value2 | part1);
                    value -= -766337246 + _secretKey[35];
                    return value;
                }
               case 135:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 14;
                    uint part2 = (uint)value << (32 - 14);
                    value = (int)(part1 | part2);
                    value ^= 1683555122 ^ salt;
                    value -= -1997614825 + _secretKey[165];
                    return value;
                }
               case 136:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 28;
                    uint part1 = (uint)value << (32 - 28);
                    value = (int)(value2 | part1);
                    value ^= -1305042504 ^ salt;
                    value = (value - _secretKey[57]) * 949096539;
                    return value;
                }
               case 137:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[192]);
                    uint part1 = value2 >> 28;
                    uint part2 = value2 << (32 - 28);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 138:
                {
                    // XorInstruction
                    value = ((value ^ 1715223135) - salt) ^ _secretKey[135];
                    return value;
                }
               case 139:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[79] - salt) * -307106491;
                    return value;
                }
               case 140:
                {
                    // AddRotateXorInstruction
                    value ^= 806320034 ^ salt;
                    uint value2 = (uint)value >> 11;
                    uint part1 = (uint)value << (32 - 11);
                    value = (int)(value2 | part1);
                    value -= -408450171 + _secretKey[11];
                    return value;
                }
               case 141:
                {
                    // AddInstruction
                    value = ((value  - -433941646) ^ salt) - _secretKey[95];
                    return value;
                }
               case 142:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 26;
                    uint part2 = (uint)value << (32 - 26);
                    value = (int)(part1 | part2);
                    value ^= 2132471747 ^ salt;
                    value -= 1775820811 + _secretKey[169];
                    return value;
                }
               case 143:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 2;
                    uint part1 = (uint)value << (32 - 2);
                    value = (int)(value2 | part1);
                    value ^= 114258470 ^ salt;
                    value = (value - _secretKey[19]) * 1566112771;
                    return value;
                }
               case 144:
                {
                    // AddRotateXorInstruction
                    value ^= -1181275232 ^ salt;
                    uint value2 = (uint)value >> 3;
                    uint part1 = (uint)value << (32 - 3);
                    value = (int)(value2 | part1);
                    value -= -650723591 + _secretKey[206];
                    return value;
                }
               case 145:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[169] - salt) * 39803307;
                    return value;
                }
               case 146:
                {
                    // XorInstruction
                    value = ((value ^ -1297404981) - salt) ^ _secretKey[154];
                    return value;
                }
               case 147:
                {
                    // MultipleRotateXorInstruction
                    value ^= 801710213 ^ salt;
                    uint value2 = (uint)value >> 18;
                    uint part1 = (uint)value << (32 - 18);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[181]) * -1217833329;
                    return value;
                }
               case 148:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 29;
                    uint part2 = (uint)value << (32 - 29);
                    value = (int)(part1 | part2);
                    value ^= 1566976773 ^ salt;
                    value -= -1933121809 + _secretKey[230];
                    return value;
                }
               case 149:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[188]);
                    uint part1 = value2 >> 30;
                    uint part2 = value2 << (32 - 30);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 150:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[53]);
                    uint part1 = value2 >> 30;
                    uint part2 = value2 << (32 - 30);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 151:
                {
                    // AddInstruction
                    value = ((value  - -2119615805) ^ salt) - _secretKey[138];
                    return value;
                }
               case 152:
                {
                    // MultipleRotateXorInstruction
                    value ^= 221292457 ^ salt;
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[102]) * -1630338257;
                    return value;
                }
               case 153:
                {
                    // MultipleRotateXorInstruction
                    value ^= 1486712056 ^ salt;
                    uint value2 = (uint)value >> 13;
                    uint part1 = (uint)value << (32 - 13);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[248]) * -1360595481;
                    return value;
                }
               case 154:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[72] - salt) * 1812161233;
                    return value;
                }
               case 155:
                {
                    // MultipleRotateXorInstruction
                    value ^= -81016400 ^ salt;
                    uint value2 = (uint)value >> 17;
                    uint part1 = (uint)value << (32 - 17);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[247]) * -47344461;
                    return value;
                }
               case 156:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1187848798 ^ salt;
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[252]) * -1858496529;
                    return value;
                }
               case 157:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 16;
                    uint part2 = (uint)value << (32 - 16);
                    value = (int)(part1 | part2);
                    value ^= 1185916334 ^ salt;
                    value -= 605454035 + _secretKey[108];
                    return value;
                }
               case 158:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 1;
                    uint part2 = (uint)value << (32 - 1);
                    value = (int)(part1 | part2);
                    value ^= -451761745 ^ salt;
                    value -= 2112611413 + _secretKey[235];
                    return value;
                }
               case 159:
                {
                    // XorInstruction
                    value = ((value ^ 1660696922) - salt) ^ _secretKey[229];
                    return value;
                }
               case 160:
                {
                    // MultipleRotateXorInstruction
                    value ^= -431219573 ^ salt;
                    uint value2 = (uint)value >> 31;
                    uint part1 = (uint)value << (32 - 31);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[111]) * 1706905775;
                    return value;
                }
               case 161:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 28;
                    uint part1 = (uint)value << (32 - 28);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[198]) * -580820577;
                    value ^= 1658933717 ^ salt;
                    return value;
                }
               case 162:
                {
                    // AddRotateXorInstruction
                    value ^= 609336148 ^ salt;
                    uint value2 = (uint)value >> 20;
                    uint part1 = (uint)value << (32 - 20);
                    value = (int)(value2 | part1);
                    value -= -1278798944 + _secretKey[72];
                    return value;
                }
               case 163:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 26;
                    uint part1 = (uint)value << (32 - 26);
                    value = (int)(value2 | part1);
                    value ^= -1055021038 ^ salt;
                    value = (value - _secretKey[57]) * -620746171;
                    return value;
                }
               case 164:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 7;
                    uint part1 = (uint)value << (32 - 7);
                    value = (int)(value2 | part1);
                    value ^= 1389308323 ^ salt;
                    value = (value - _secretKey[225]) * 2035776477;
                    return value;
                }
               case 165:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value -= -1939584600 + _secretKey[124];
                    value ^= 318043677 ^ salt;
                    return value;
                }
               case 166:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value ^= 2047590880 ^ salt;
                    value = (value - _secretKey[184]) * -748679859;
                    return value;
                }
               case 167:
                {
                    // XorInstruction
                    value = ((value ^ -312123044) - salt) ^ _secretKey[114];
                    return value;
                }
               case 168:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 1;
                    uint part2 = (uint)value << (32 - 1);
                    value = (int)(part1 | part2);
                    value ^= -1299860280 ^ salt;
                    value -= 1897551751 + _secretKey[139];
                    return value;
                }
               case 169:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[10]);
                    uint part1 = value2 >> 15;
                    uint part2 = value2 << (32 - 15);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 170:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[60] - salt) * 609589815;
                    return value;
                }
               case 171:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 21;
                    uint part1 = (uint)value << (32 - 21);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[242]) * 893240649;
                    value ^= 820953326 ^ salt;
                    return value;
                }
               case 172:
                {
                    // XorInstruction
                    value = ((value ^ 921116076) - salt) ^ _secretKey[89];
                    return value;
                }
               case 173:
                {
                    // AddInstruction
                    value = ((value  - -1261901861) ^ salt) - _secretKey[82];
                    return value;
                }
               case 174:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1758870671 ^ salt;
                    uint value2 = (uint)value >> 1;
                    uint part1 = (uint)value << (32 - 1);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[42]) * -1163763527;
                    return value;
                }
               case 175:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value -= 1254595032 + _secretKey[10];
                    value ^= -99371457 ^ salt;
                    return value;
                }
               case 176:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 0;
                    uint part2 = (uint)value << (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= -2036462975 ^ salt;
                    value -= 1901168605 + _secretKey[131];
                    return value;
                }
               case 177:
                {
                    // MultipleRotateXorInstruction
                    value ^= -805817000 ^ salt;
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[29]) * -1809334639;
                    return value;
                }
               case 178:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 12;
                    uint part2 = (uint)value << (32 - 12);
                    value = (int)(part1 | part2);
                    value ^= 762578411 ^ salt;
                    value -= 28490730 + _secretKey[13];
                    return value;
                }
               case 179:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 24;
                    uint part1 = (uint)value << (32 - 24);
                    value = (int)(value2 | part1);
                    value -= -41308497 + _secretKey[85];
                    value ^= 1812019570 ^ salt;
                    return value;
                }
               case 180:
                {
                    // XorInstruction
                    value = ((value ^ 1563652208) - salt) ^ _secretKey[231];
                    return value;
                }
               case 181:
                {
                    // AddRotateXorInstruction
                    value ^= -1148350591 ^ salt;
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value -= -542613261 + _secretKey[75];
                    return value;
                }
               case 182:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 19;
                    uint part2 = (uint)value << (32 - 19);
                    value = (int)(part1 | part2);
                    value ^= 1842755263 ^ salt;
                    value -= 1462569147 + _secretKey[135];
                    return value;
                }
               case 183:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 8;
                    uint part1 = (uint)value << (32 - 8);
                    value = (int)(value2 | part1);
                    value ^= -159244912 ^ salt;
                    value = (value - _secretKey[15]) * -663204867;
                    return value;
                }
               case 184:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[230]);
                    uint part1 = value2 >> 5;
                    uint part2 = value2 << (32 - 5);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 185:
                {
                    // AddRotateXorInstruction
                    value ^= 1218428368 ^ salt;
                    uint value2 = (uint)value >> 5;
                    uint part1 = (uint)value << (32 - 5);
                    value = (int)(value2 | part1);
                    value -= 45305078 + _secretKey[119];
                    return value;
                }
               case 186:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 0;
                    uint part2 = (uint)value << (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= 567100030 ^ salt;
                    value -= 148564506 + _secretKey[160];
                    return value;
                }
               case 187:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[242]);
                    uint part1 = value2 >> 21;
                    uint part2 = value2 << (32 - 21);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 188:
                {
                    // MultipleRotateXorInstruction
                    value ^= 730302816 ^ salt;
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[30]) * -1488719571;
                    return value;
                }
               case 189:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[93]);
                    uint part1 = value2 >> 18;
                    uint part2 = value2 << (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 190:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[69]);
                    uint part1 = value2 >> 29;
                    uint part2 = value2 << (32 - 29);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 191:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[153]) * 1590990225;
                    value ^= 1474104403 ^ salt;
                    return value;
                }
               case 192:
                {
                    // AddInstruction
                    value = ((value  - 1345231273) ^ salt) - _secretKey[170];
                    return value;
                }
               case 193:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[138]);
                    uint part1 = value2 >> 28;
                    uint part2 = value2 << (32 - 28);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 194:
                {
                    // AddRotateXorInstruction
                    value ^= -1086752221 ^ salt;
                    uint value2 = (uint)value >> 2;
                    uint part1 = (uint)value << (32 - 2);
                    value = (int)(value2 | part1);
                    value -= -1649692985 + _secretKey[138];
                    return value;
                }
               case 195:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 5;
                    uint part1 = (uint)value << (32 - 5);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[23]) * -718602987;
                    value ^= 216360478 ^ salt;
                    return value;
                }
               case 196:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 25;
                    uint part1 = (uint)value << (32 - 25);
                    value = (int)(value2 | part1);
                    value -= -922639548 + _secretKey[211];
                    value ^= 1736125070 ^ salt;
                    return value;
                }
               case 197:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value -= -1729078426 + _secretKey[124];
                    value ^= -1590872932 ^ salt;
                    return value;
                }
               case 198:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 13;
                    uint part2 = (uint)value << (32 - 13);
                    value = (int)(part1 | part2);
                    value ^= 2101329043 ^ salt;
                    value -= -2086916257 + _secretKey[135];
                    return value;
                }
               case 199:
                {
                    // XorInstruction
                    value = ((value ^ -1188487898) - salt) ^ _secretKey[133];
                    return value;
                }
               case 200:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[162] - salt) * -1592735389;
                    return value;
                }
               case 201:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 11;
                    uint part1 = (uint)value << (32 - 11);
                    value = (int)(value2 | part1);
                    value -= -1882888353 + _secretKey[153];
                    value ^= 2087683186 ^ salt;
                    return value;
                }
               case 202:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1855365205 ^ salt;
                    uint value2 = (uint)value >> 22;
                    uint part1 = (uint)value << (32 - 22);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[58]) * -1429384213;
                    return value;
                }
               case 203:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[66] - salt) * 180652695;
                    return value;
                }
               case 204:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 0;
                    uint part1 = (uint)value << (32 - 0);
                    value = (int)(value2 | part1);
                    value ^= 1668989123 ^ salt;
                    value = (value - _secretKey[206]) * 715760713;
                    return value;
                }
               case 205:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[169] - salt) * -19415893;
                    return value;
                }
               case 206:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[143]) * 408562579;
                    value ^= -387621173 ^ salt;
                    return value;
                }
               case 207:
                {
                    // XorInstruction
                    value = ((value ^ -1302837102) - salt) ^ _secretKey[133];
                    return value;
                }
               case 208:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 29;
                    uint part1 = (uint)value << (32 - 29);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[5]) * 199587543;
                    value ^= 1201861103 ^ salt;
                    return value;
                }
               case 209:
                {
                    // AddInstruction
                    value = ((value  - -1698116194) ^ salt) - _secretKey[188];
                    return value;
                }
               case 210:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 3;
                    uint part1 = (uint)value << (32 - 3);
                    value = (int)(value2 | part1);
                    value -= -5412811 + _secretKey[40];
                    value ^= 955827838 ^ salt;
                    return value;
                }
               case 211:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value ^= -261186202 ^ salt;
                    value = (value - _secretKey[206]) * 250403797;
                    return value;
                }
               case 212:
                {
                    // XorInstruction
                    value = ((value ^ 1451245279) - salt) ^ _secretKey[215];
                    return value;
                }
               case 213:
                {
                    // AddInstruction
                    value = ((value  - -48271475) ^ salt) - _secretKey[248];
                    return value;
                }
               case 214:
                {
                    // MultipleRotateXorInstruction
                    value ^= 280704379 ^ salt;
                    uint value2 = (uint)value >> 31;
                    uint part1 = (uint)value << (32 - 31);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[72]) * -1743417391;
                    return value;
                }
               case 215:
                {
                    // MultipleRotateXorInstruction
                    value ^= -1507466225 ^ salt;
                    uint value2 = (uint)value >> 17;
                    uint part1 = (uint)value << (32 - 17);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[176]) * -795921711;
                    return value;
                }
               case 216:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[162]);
                    uint part1 = value2 >> 23;
                    uint part2 = value2 << (32 - 23);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 217:
                {
                    // XorInstruction
                    value = ((value ^ -1329546797) - salt) ^ _secretKey[108];
                    return value;
                }
               case 218:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 11;
                    uint part1 = (uint)value << (32 - 11);
                    value = (int)(value2 | part1);
                    value -= 1710889501 + _secretKey[85];
                    value ^= 846489904 ^ salt;
                    return value;
                }
               case 219:
                {
                    // XorInstruction
                    value = ((value ^ -339712479) - salt) ^ _secretKey[127];
                    return value;
                }
               case 220:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 15;
                    uint part1 = (uint)value << (32 - 15);
                    value = (int)(value2 | part1);
                    value -= -308188673 + _secretKey[78];
                    value ^= -1008587035 ^ salt;
                    return value;
                }
               case 221:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[111] - salt) * 773277731;
                    return value;
                }
               case 222:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[198] - salt) * 1149219487;
                    return value;
                }
               case 223:
                {
                    // AddInstruction
                    value = ((value  - -449129672) ^ salt) - _secretKey[160];
                    return value;
                }
               case 224:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 12;
                    uint part1 = (uint)value << (32 - 12);
                    value = (int)(value2 | part1);
                    value ^= -180027834 ^ salt;
                    value = (value - _secretKey[84]) * 102372989;
                    return value;
                }
               case 225:
                {
                    // XorInstruction
                    value = ((value ^ 946019090) - salt) ^ _secretKey[58];
                    return value;
                }
               case 226:
                {
                    // AddRotateXorInstruction
                    value ^= 1350981383 ^ salt;
                    uint value2 = (uint)value >> 3;
                    uint part1 = (uint)value << (32 - 3);
                    value = (int)(value2 | part1);
                    value -= 694016884 + _secretKey[225];
                    return value;
                }
               case 227:
                {
                    // AddRotateXorInstruction
                    value ^= 1680252929 ^ salt;
                    uint value2 = (uint)value >> 28;
                    uint part1 = (uint)value << (32 - 28);
                    value = (int)(value2 | part1);
                    value -= -870643939 + _secretKey[168];
                    return value;
                }
               case 228:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[184]);
                    uint part1 = value2 >> 4;
                    uint part2 = value2 << (32 - 4);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 229:
                {
                    // AddRotateXorInstruction
                    value ^= -1727043214 ^ salt;
                    uint value2 = (uint)value >> 28;
                    uint part1 = (uint)value << (32 - 28);
                    value = (int)(value2 | part1);
                    value -= 1785715822 + _secretKey[199];
                    return value;
                }
               case 230:
                {
                    // XorInstruction
                    value = ((value ^ -1277148537) - salt) ^ _secretKey[139];
                    return value;
                }
               case 231:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 10;
                    uint part1 = (uint)value << (32 - 10);
                    value = (int)(value2 | part1);
                    value ^= 1940873679 ^ salt;
                    value = (value - _secretKey[226]) * -528829791;
                    return value;
                }
               case 232:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 14;
                    uint part1 = (uint)value << (32 - 14);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[171]) * 1486956053;
                    value ^= -879839609 ^ salt;
                    return value;
                }
               case 233:
                {
                    // AddInstruction
                    value = ((value  - 328489970) ^ salt) - _secretKey[181];
                    return value;
                }
               case 234:
                {
                    // MultipleInstruction
                    value = (value - _secretKey[89] - salt) * -1387476699;
                    return value;
                }
               case 235:
                {
                    // XorAddRotateInstruction
                    uint value2 = (uint)value >> 8;
                    uint part1 = (uint)value << (32 - 8);
                    value = (int)(value2 | part1);
                    value -= -1977097134 + _secretKey[203];
                    value ^= 1290000091 ^ salt;
                    return value;
                }
               case 236:
                {
                    // AddInstruction
                    value = ((value  - 1890859361) ^ salt) - _secretKey[113];
                    return value;
                }
               case 237:
                {
                    // AddRotateXorInstruction
                    value ^= 1434413518 ^ salt;
                    uint value2 = (uint)value >> 10;
                    uint part1 = (uint)value << (32 - 10);
                    value = (int)(value2 | part1);
                    value -= 1045620543 + _secretKey[216];
                    return value;
                }
               case 238:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 0;
                    uint part2 = (uint)value << (32 - 0);
                    value = (int)(part1 | part2);
                    value ^= 1591345537 ^ salt;
                    value -= -1706485027 + _secretKey[131];
                    return value;
                }
               case 239:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 24;
                    uint part2 = (uint)value << (32 - 24);
                    value = (int)(part1 | part2);
                    value ^= 1117669949 ^ salt;
                    value -= 1271081841 + _secretKey[29];
                    return value;
                }
               case 240:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 12;
                    uint part1 = (uint)value << (32 - 12);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[235]) * 1588287429;
                    value ^= -842525462 ^ salt;
                    return value;
                }
               case 241:
                {
                    // AddInstruction
                    value = ((value  - 1030822002) ^ salt) - _secretKey[175];
                    return value;
                }
               case 242:
                {
                    // MultipleRotateXorInstruction
                    value ^= 811922151 ^ salt;
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[57]) * 769862473;
                    return value;
                }
               case 243:
                {
                    // AddRotateXorInstruction
                    value ^= -1258702719 ^ salt;
                    uint value2 = (uint)value >> 23;
                    uint part1 = (uint)value << (32 - 23);
                    value = (int)(value2 | part1);
                    value -= 656680947 + _secretKey[75];
                    return value;
                }
               case 244:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 19;
                    uint part1 = (uint)value << (32 - 19);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[191]) * -883345609;
                    value ^= 90809787 ^ salt;
                    return value;
                }
               case 245:
                {
                    // AddRotateXorInstruction
                    value ^= 99436168 ^ salt;
                    uint value2 = (uint)value >> 16;
                    uint part1 = (uint)value << (32 - 16);
                    value = (int)(value2 | part1);
                    value -= -532913580 + _secretKey[15];
                    return value;
                }
               case 246:
                {
                    // MultipleXorRotateInstruction
                    uint value2 = (uint)value >> 22;
                    uint part1 = (uint)value << (32 - 22);
                    value = (int)(value2 | part1);
                    value ^= 1471598712 ^ salt;
                    value = (value - _secretKey[230]) * -599880499;
                    return value;
                }
               case 247:
                {
                    // XorMultipleRotateInstruction
                    uint value2 = (uint)value >> 26;
                    uint part1 = (uint)value << (32 - 26);
                    value = (int)(value2 | part1);
                    value = (value - _secretKey[15]) * -1447936463;
                    value ^= 1941306053 ^ salt;
                    return value;
                }
               case 248:
                {
                    // AddRotateXorInstruction
                    value ^= 2045091157 ^ salt;
                    uint value2 = (uint)value >> 18;
                    uint part1 = (uint)value << (32 - 18);
                    value = (int)(value2 | part1);
                    value -= 883137918 + _secretKey[96];
                    return value;
                }
               case 249:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[165]);
                    uint part1 = value2 >> 13;
                    uint part2 = value2 << (32 - 13);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 250:
                {
                    // AddInstruction
                    value = ((value  - -394947456) ^ salt) - _secretKey[96];
                    return value;
                }
               case 251:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[93]);
                    uint part1 = value2 >> 18;
                    uint part2 = value2 << (32 - 18);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 252:
                {
                    // AddInstruction
                    value = ((value  - 1917332797) ^ salt) - _secretKey[69];
                    return value;
                }
               case 253:
                {
                    // AddXorRotateInstruction
                    uint part1 = (uint)value >> 14;
                    uint part2 = (uint)value << (32 - 14);
                    value = (int)(part1 | part2);
                    value ^= -1509317223 ^ salt;
                    value -= 1006809939 + _secretKey[113];
                    return value;
                }
               case 254:
                {
                    // BitRotateInstruction
                    uint value2 = (uint)((value - salt) ^ _secretKey[170]);
                    uint part1 = value2 >> 9;
                    uint part2 = value2 << (32 - 9);
                    value = (int)(part1 | part2);
                    return value;
                }
               case 255:
                {
                    // AddInstruction
                    value = ((value  - 683715132) ^ salt) - _secretKey[138];
                    return value;
                }

                default:
                    throw new System.Exception($"Invalid opCode:{opCode}");
            }
        }

    }
}

