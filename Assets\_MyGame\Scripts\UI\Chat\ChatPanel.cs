using System;
using System.Collections.Generic;
using System.IO;
using Common.NetMsg;
using DashGame;
using FairyGUI;
using Google.Protobuf.Collections;
using LitJson;
using Proto.LogicData;
using UnityEngine;
using UnityEngine.PlayerLoop;

public class ChatPanel : Panel
{
    private GTree listChatContent;
    private GTreeNode rootNode;
    private GTextInput inputCode;

    private DictList<string, List<NetChatMsgRecord>> worldChatList;
    private DictList<string, List<NetChatMsgRecord>> theaterChatList;
    private DictList<string, List<NetChatMsgRecord>> guildChatList;

    public float ChatIntervals; // 聊天间隔
    long lastWorldChatTime;
    long lastTheaterChatTime;
    long lastGuildChatTime;

    private int activeChatIndex; // 0: World, 1: Theater, 2: Guild
    private GList listTab;

    private GComponent boxSticker;
    private GList listEmoji;
    private GList listSticker;
    private GList listStickerGroup;
    private int activeStickerGroupIndex;
    public int emojiLength = 6; // emoji占用字符长度
    private DictList<int, List<InfoSticker>> stickerConfigDict;
    private List<int> stickerGroupIds; // 贴纸分组ID列表

    private long nowFriendUid; // 当前聊天好友信息
    private GList listChatFriends;
    private GTree listChatContentF;
    private GTreeNode rootNodeF;
    private DictList<long, List<NetChatMsgRecord>> friendChatList; // 好友聊天记录
    private DictList<long, NetFriendInfo> friendInfoList;
    private List<NetFriendInfo> talkedFriendList;

    private RepeatedField<int> chatEmoticonList; // 聊天表情包列表

    private GObject friendChatRedDot;

    public ChatPanel()
    {
        packName = "Chat";
        compName = "ChatPanel";
        extraPacks = new string[] { "ChatSticker", "ChatDeco" };
        modal = true;
    }

    protected override void DoInitialize()
    {
        listChatContent = contentPane.GetChild("listChatContent").asTree;
        rootNode = listChatContent.rootNode;
        inputCode = contentPane.GetChild("inputCode").asTextInput;

        worldChatList = new DictList<string, List<NetChatMsgRecord>>();
        theaterChatList = new DictList<string, List<NetChatMsgRecord>>();
        guildChatList = new DictList<string, List<NetChatMsgRecord>>();

        listTab = contentPane.GetChild("listTab").asList;
        listTab.onClickItem.Add(OnClickTabItem);

        boxSticker = contentPane.GetChild("boxSticker").asCom;
        listEmoji = boxSticker.GetChild("listEmoji").asList;
        listEmoji.itemRenderer = OnUpdateEmojiItem;
        listSticker = boxSticker.GetChild("listSticker").asList;
        listStickerGroup = boxSticker.GetChild("listStickerGroup").asList;
        stickerConfigDict = new DictList<int, List<InfoSticker>>();

        stickerGroupIds = new List<int>();

        // 好友聊天相关初始化
        listChatFriends = contentPane.GetChild("listChatFriends").asList;
        listChatFriends.itemRenderer = OnUpdateChatFriendItem;
        listChatContentF = contentPane.GetChild("listChatContentF").asTree;
        rootNodeF = listChatContentF.rootNode;
        friendChatList = new DictList<long, List<NetChatMsgRecord>>();
        friendInfoList = new DictList<long, NetFriendInfo>();
        talkedFriendList = new List<NetFriendInfo>();

        friendChatRedDot = listTab.GetChildAt(3).asCom.GetChild("imgRedDot");

        Stage.inst.onTouchBegin.Add(OnGlobalTouchBegin);

        Request();
        UpdateChatUI();
        UpdateRedDot();
    }

    private void UpdateRedDot()
    {
        friendChatRedDot.visible = RedDotUtil.GetInstance().GetRedDotStatus((int)RedPointType.FriendChat);
    }

    private void OnGlobalTouchBegin(EventContext context)
    {
        if (!boxSticker.visible) return;

        GObject target = GRoot.inst.touchTarget;

        bool isClickInsideStickerPanel = false;
        while (target != null)
        {
            if (target == boxSticker || target == listStickerGroup)
            {
                isClickInsideStickerPanel = true;
                break;
            }
            target = target.parent;
        }

        if (!isClickInsideStickerPanel)
        {
            boxSticker.visible = false;
        }
    }

    private void OnRenderStickerGroup(int index, GObject obj)
    {
        int groupID = stickerGroupIds[index];
        var tabIcon = obj.asCom.GetChild("icon").asLoader;
        tabIcon.url = PathUtil.GetChatTabUrl(groupID);
    }

    private void OnUpdateEmojiItem(int index, GObject obj)
    {
        var data = (obj.parent.data as List<InfoSticker>)[index];
        var icon = obj.asCom.GetChild("icon").asLoader;
        icon.url = PathUtil.GetStickerUrl(data.effectID);
        obj.onClick.Clear();
        obj.onClick.Add(() =>
        {
            string emojiName = data.name;
            inputCode.text += emojiName;
            boxSticker.visible = false;
        });
    }

    private void OnUpdateChatFriendItem(int index, GObject obj)
    {
        var friendInfo = talkedFriendList[index];
        var avatar = obj.asCom.GetChild("avatar").asCom;
        var icon = avatar.GetChild("icon").asLoader;
        var iconFrame = avatar.GetChild("iconFrame").asLoader;
        var lblPlayerName = obj.asCom.GetChild("lblPlayerName").asTextField;
        var reputationTitle = obj.asCom.GetChild("reputationTitle").asLoader;
        var btnClose = obj.asCom.GetChild("btnClose").asButton;

        if (friendInfo.Icon == "")
        {
            icon.url = PathUtil.GetHeadIcon(0.ToString());
        }
        else
        {
            icon.url = PathUtil.GetHeadIcon(friendInfo.Icon);
        }
        if (friendInfo.IconFrame == 0)
        {
            iconFrame.url = PathUtil.GetHeadFrame(3400);
        }
        else
        {
            iconFrame.url = PathUtil.GetHeadFrame((int)friendInfo.IconFrame);
        }
        lblPlayerName.text = friendInfo.PlayerName;
        if (friendInfo.VipLevel > 0)
        {
            reputationTitle.visible = true;
            reputationTitle.url = PathUtil.GetItemIcon("image_title_" + friendInfo.VipLevel);
        }
        else
        {
            reputationTitle.visible = false;
        }
        btnClose.onClick.Clear();
        btnClose.onClick.Add((EventContext context) =>
        {
            context.StopPropagation();
            OnFriendCloseClick(friendInfo.PlayerUid);
        });

        obj.onClick.Clear();
        obj.onClick.Add(() =>
        {
            if (GRoot.inst.touchTarget != btnClose)
            {
                OnFriendClick(friendInfo.PlayerUid);
                listChatFriends.selectedIndex = index;
            }
        });
    }

    private void Request()
    {
        NetMgr.C2SRequestChatMsgList(0);
    }

    protected override void OnMessageRecieve(NetMessage msg)
    {
        var cmd = msg.GetCmd();
        switch (cmd)
        {
            case NetMsg.S2CRequestChatMsgListResult:
                var listInfo = msg.Parse<RequestChatMsgListResult>();
                ChatIntervals = listInfo.ChatCdTime;
                var worldList = listInfo.WorldMsgList;
                var areaList = listInfo.AreaMsgList;
                var guildList = listInfo.GuildMsgList;

                worldChatList.Clear();
                theaterChatList.Clear();
                guildChatList.Clear();
                ParseChatListInfo(worldList);
                ParseChatListInfo(areaList);
                ParseChatListInfo(guildList);

                var newTalkedFriends = listInfo.TalkedFriendList;
                foreach (var friend in newTalkedFriends)
                {
                    if (!talkedFriendList.Exists(x => x.PlayerUid == friend.PlayerUid))
                    {
                        talkedFriendList.Insert(0, friend);
                    }
                    UpdateFriendInfoList(friend);
                }

                var friendMsgList = listInfo.FriendMsgList;
                friendChatList.Clear();
                foreach (var item in friendMsgList)
                {
                    ParseFriendChatListInfo(item.MsgList);
                }

                chatEmoticonList = listInfo.ChatEmoticonList;
                var stickerDict = ConfigSticker.GetAllList();
                for (int i = 0; i < stickerDict.Count; i++)
                {
                    var groupID = stickerDict.GetKeyByIndex(i);
                    var stickerList = stickerDict.GetValueByIndex(i);

                    if (groupID == 1 || groupID == 1000)
                    {
                        stickerGroupIds.Add(groupID);
                        if (groupID != 1)
                        {
                            stickerConfigDict.Add(groupID, stickerList);
                        }
                    }
                    else
                    {
                        if (chatEmoticonList.Contains(groupID))
                        {
                            stickerGroupIds.Add(groupID);
                            stickerConfigDict.Add(groupID, stickerList);
                        }
                    }
                }

                listStickerGroup.itemRenderer = OnRenderStickerGroup;
                listStickerGroup.data = stickerGroupIds;
                listStickerGroup.numItems = stickerGroupIds.Count;
                listStickerGroup.onClickItem.Add(OnClickStickerGroup);

                listStickerGroup.selectedIndex = 0;
                activeStickerGroupIndex = 0;

                listEmoji.visible = true;
                listSticker.visible = false;

                var emojiGroup = ConfigSticker.GetListByGroup(1);
                listEmoji.data = emojiGroup;
                listEmoji.numItems = emojiGroup.Count;
                break;
            case NetMsg.S2CRequestSendChatMsgResult:
                var chatMsgResult = msg.Parse<RequestSendChatMsgResult>();
                if (chatMsgResult.Result == ErrorCode.Success)
                {

                }
                else
                {
                    TipMgr.ShowErrorCodeTip(chatMsgResult.Result);
                }
                break;
            case NetMsg.S2CUpdateNewChatMsgResult:
                var updateInfo = msg.Parse<UpdateNewChatMsgResult>();
                var updateList = updateInfo.NewChatMsg;

                if (updateInfo.ChannelType == 4)
                {
                    var friendInfo = updateInfo.FriendInfo;
                    UpdateFriendInfoList(friendInfo);
                    ParseFriendChatUpdateInfo(updateList);
                }
                else
                {
                    ParseChatUpdateInfo(updateList);
                }
                break;
        }
    }

    private void UpdateFriendInfoList(NetFriendInfo friendInfo)
    {
        if (!friendInfoList.ContainsKey(friendInfo.PlayerUid))
        {
            friendInfoList.Add(friendInfo.PlayerUid, friendInfo);
        }
        else
        {
            friendInfoList.GetValueByKey(friendInfo.PlayerUid).Icon = friendInfo.Icon;
            friendInfoList.GetValueByKey(friendInfo.PlayerUid).IconFrame = friendInfo.IconFrame;
            friendInfoList.GetValueByKey(friendInfo.PlayerUid).Level = friendInfo.Level;
            friendInfoList.GetValueByKey(friendInfo.PlayerUid).VipLevel = friendInfo.VipLevel;
        }
    }

    private void ParseFriendChatListInfo(RepeatedField<NetChatMsgRecord> list)
    {
        for (int i = 0; i < list.Count; i++)
        {
            var item = list[i];
            long friendUid;

            if (item.FromPlayerInfo.PlayerUid == Session.playerLobbyInfo.PlayerUid)
            {
                friendUid = item.ToPlayerUid;
                if (!friendInfoList.ContainsKey(friendUid))
                {
                    friendInfoList.Add(friendUid, new NetFriendInfo
                    {
                        PlayerUid = friendUid,
                        PlayerName = "未知好友",
                    });
                }
            }
            else
            {
                friendUid = item.FromPlayerInfo.PlayerUid;
                var friendInfo = new NetFriendInfo
                {
                    PlayerUid = item.FromPlayerInfo.PlayerUid,
                    PlayerName = item.FromPlayerInfo.Name,
                    Icon = item.FromPlayerInfo.Icon,
                    IconFrame = (uint)item.FromPlayerInfo.IconFrame,
                    Level = item.FromPlayerInfo.Level,
                    VipLevel = (uint)item.FromPlayerInfo.VipLevel
                };
                UpdateFriendInfoList(friendInfo);
            }

            if (!friendChatList.ContainsKey(friendUid))
            {
                friendChatList.Add(friendUid, new List<NetChatMsgRecord>());
            }
            friendChatList.GetValueByKey(friendUid).Add(item);
        }
        UpdateFriendChat();
    }

    private void ParseFriendChatUpdateInfo(NetChatMsgRecord updateList)
    {
        if (updateList.FromPlayerInfo.PlayerUid == Session.playerLobbyInfo.PlayerUid)
        {
            if (!friendChatList.ContainsKey(updateList.ToPlayerUid))
            {
                friendChatList.Add(updateList.ToPlayerUid, new List<NetChatMsgRecord>());
            }
            friendChatList.GetValueByKey(updateList.ToPlayerUid).Insert(0, updateList);
        }
        else
        {
            if (!friendChatList.ContainsKey(updateList.FromPlayerInfo.PlayerUid))
            {
                friendChatList.Add(updateList.FromPlayerInfo.PlayerUid, new List<NetChatMsgRecord>());
            }
            friendChatList.GetValueByKey(updateList.FromPlayerInfo.PlayerUid).Insert(0, updateList);
        }

        UpdateFriendChat();
    }

    private void UpdateFriendChat()
    {
        listChatFriends.data = talkedFriendList;
        listChatFriends.numItems = talkedFriendList.Count;

        var boxNotData = contentPane.GetChild("boxNotData").asGroup;
        boxNotData.visible = talkedFriendList.Count == 0;

        if (talkedFriendList.Count > 0)
        {
            int index = -1;
            for (int i = 0; i < talkedFriendList.Count; i++)
            {
                if (talkedFriendList[i].PlayerUid == nowFriendUid)
                {
                    index = i;
                    break;
                }
            }

            if (index == -1)
            {
                index = talkedFriendList.Count - 1;
                nowFriendUid = talkedFriendList[index].PlayerUid;
            }

            listChatFriends.selectedIndex = index;

            listChatFriends.scrollPane.ScrollToView(listChatFriends.GetChildAt(index), true);
            OnUpdateChatFirendContent(talkedFriendList[index]);
        }
        else
        {
            rootNodeF.RemoveChildren();
        }

        UpdateDataHint();
    }


    private void OnUpdateChatFirendContent(NetFriendInfo info)
    {
        if (friendChatList.ContainsKey(info.PlayerUid))
        {
            var chatMsgList = friendChatList.GetValueByKey(info.PlayerUid);
            UpdateFriendChatUI(chatMsgList);
        }
        else
        {
            rootNodeF.RemoveChildren();
            UpdateDataHint();
        }
    }

    private void UpdateFriendChatUI(List<NetChatMsgRecord> infoList)
    {
        rootNodeF.RemoveChildren();
        var chatList = new DictList<string, List<NetChatMsgRecord>>();

        for (int i = 0; i < infoList.Count; i++)
        {
            ParseChatInfo(infoList[i], chatList);
        }

        for (int i = chatList.Count - 1; i >= 0; i--)
        {
            var key = chatList.GetKeyByIndex(i);
            var list = chatList.GetValueByKey(key);

            var timeNodeF = CreateTimeNodeF(key);
            for (int j = list.Count - 1; j >= 0; j--)
            {
                var info = list[j];
                var playerName = info.FromPlayerInfo.Name;
                var theaterRank = info.FromPlayerInfo.AreaId.ToString();
                var vipLevel = (int)info.FromPlayerInfo.VipLevel;
                var iconID = info.FromPlayerInfo.Icon;
                var iconFrameID = info.FromPlayerInfo.IconFrame;
                var decoID = info.FromPlayerInfo.ChatBg;

                int fromWho;
                if (info.FromPlayerInfo.PlayerUid == Session.playerLobbyInfo.PlayerUid)
                {
                    fromWho = 0;
                }
                else
                {
                    fromWho = 1;
                }

                if (info.MsgType == 3)
                {
                    var stickerName = info.ChatMsg;
                    CreateStickerNodeF(timeNodeF, playerName, stickerName, theaterRank, iconID, iconFrameID, decoID, fromWho, vipLevel);
                }
                else
                {
                    var content = info.ChatMsg;
                    CreateContentNodeF(timeNodeF, playerName, content, theaterRank, iconID, iconFrameID, decoID, fromWho, vipLevel);
                }
            }
        }

        UpdateDataHint();

        listChatContentF.scrollPane.ScrollBottom();
    }

    private void UpdateDataHint()
    {
        var boxNotData = contentPane.GetChild("boxNotData").asGroup;

        if (activeChatIndex == 3)
        {
            boxNotData.visible = talkedFriendList.Count == 0;
        }
        else
        {
            switch (activeChatIndex)
            {
                case 0:
                    boxNotData.visible = worldChatList.Count == 0;
                    break;
                case 1:
                    boxNotData.visible = theaterChatList.Count == 0;
                    break;
                case 2:
                    boxNotData.visible = guildChatList.Count == 0;
                    break;
            }
        }
    }

    private void CreateStickerNodeF(GTreeNode parentNode, string playerName, string stickerName, string theaterRank, string iconID, int iconFrameID, string decoID, int fromWho, int vipLevel)
    {
        GTreeNode stickerNodeF = null;

        if (fromWho == 0)
        {
            stickerNodeF = new GTreeNode(false, GetCurPackRes("ChatMyItemStickerF"));
        }
        else
        {
            stickerNodeF = new GTreeNode(false, GetCurPackRes("ChatOtherItemStickerF"));
        }

        parentNode.AddChild(stickerNodeF);

        var info = stickerNodeF.cell.GetChild("info").asCom;
        var lblPlayerName = info.GetChild("lblPlayerName").asTextField;
        var lblRankGroup = info.GetChild("lblRankGroup").asTextField;

        lblPlayerName.text = playerName;
        lblRankGroup.text = theaterRank;

        var avatar = info.GetChild("avatar").asCom;
        var icon = avatar.GetChild("icon").asLoader;
        var iconFrame = avatar.GetChild("iconFrame").asLoader;
        if (iconID == "")
        {
            icon.url = PathUtil.GetHeadIcon(0.ToString());
        }
        else
        {
            icon.url = PathUtil.GetHeadIcon(iconID);
        }
        if (iconFrameID == 0) // 空数据?
        {
            iconFrame.url = PathUtil.GetHeadFrame(3400);
        }
        else
        {
            iconFrame.url = PathUtil.GetHeadFrame(iconFrameID);
        }

        var chatBox = stickerNodeF.cell.GetChild("chatBox").asLoader;
        var chatDecoInfo = ConfigChatDeco.GetData(decoID);
        var resName = chatDecoInfo.resName;
        chatBox.url = PathUtil.GetChatDecoUrl(resName);
        chatBox.image.graphics.flip = fromWho == 0 ? FlipType.Horizontal: FlipType.None;

        var effectSticker = stickerNodeF.cell.GetChild("effectSticker").asLoader;

        string effectID = ConfigSticker.GetEffectIDByName(stickerName);
        if (!string.IsNullOrEmpty(effectID))
        {
            effectSticker.url = PathUtil.GetStickerUrl(effectID);
        }
        else
        {
            Debug.LogError($"找不到贴纸: {stickerName}");
            effectSticker.url = "";
        }

        var reputationTitle = info.GetChild("reputationTitle").asLoader;
        if (vipLevel > 0)
        {
            reputationTitle.visible = true;
            reputationTitle.url = PathUtil.GetItemIcon("image_title_" + vipLevel);
        }
        else
        {
            reputationTitle.visible = false;
        }

        listChatContentF.scrollPane.ScrollBottom();
    }

    private void CreateContentNodeF(GTreeNode parentNode, string playerName, string content, string theaterRank, string iconID, int iconFrameID, string decoID, int fromWho, int vipLevel)
    {
        GTreeNode contentNodeF = new GTreeNode(false, GetCurPackRes("ChatContentItemF"));
        parentNode.AddChild(contentNodeF);

        var c1 = contentNodeF.cell.GetController("c1");
        c1.selectedIndex = fromWho;

        GComponent chatItem;
        if (fromWho == 0)
        {
            chatItem = contentNodeF.cell.GetChild("chatMyItem").asCom;
        }
        else
        {
            chatItem = contentNodeF.cell.GetChild("chatOtherItem").asCom;
        }

        var chatItemInfo = chatItem.GetChild("info").asCom;
        var lblPlayerName = chatItemInfo.GetChild("lblPlayerName").asTextField;
        var lblRankGroup = chatItemInfo.GetChild("lblRankGroup").asTextField;
        var lblChatContent = chatItem.GetChild("lblChatContent").asRichTextField;

        lblPlayerName.text = playerName;
        lblRankGroup.text = theaterRank;

        bool hasEmj = content.Contains("[bq");
        if (hasEmj)
        {
            System.Text.RegularExpressions.Regex regex = new System.Text.RegularExpressions.Regex(@"\[bq(\d{2})\]");

            string replacedContent = regex.Replace(content, match =>
            {
                string emojiNumber = match.Groups[1].Value;

                string emojiName = "[bq" + emojiNumber + "]";

                string effectID = ConfigSticker.GetEffectIDByName(emojiName);

                if (string.IsNullOrEmpty(effectID))
                {
                    Debug.LogWarning($"找不到表情: {emojiName}");
                    return match.Value;
                }

                return $"<img src='{PathUtil.GetStickerUrl(effectID)}' width='40' height='40'/>";
            });

            lblChatContent.text = replacedContent;
        }
        else
        {
            lblChatContent.text = content;
        }

        var avatar = chatItemInfo.GetChild("avatar").asCom;
        var icon = avatar.GetChild("icon").asLoader;
        var iconFrame = avatar.GetChild("iconFrame").asLoader;
        if (iconID == "")
        {
            icon.url = PathUtil.GetHeadIcon(0.ToString());
        }
        else
        {
            icon.url = PathUtil.GetHeadIcon(iconID);
        }
        if (iconFrameID == 0) // 空数据?
        {
            iconFrame.url = PathUtil.GetHeadFrame(3400);
        }
        else
        {
            iconFrame.url = PathUtil.GetHeadFrame(iconFrameID);
        }

        var chatDecoInfo = ConfigChatDeco.GetData(decoID);
        Color txtColor;
        if (ColorUtility.TryParseHtmlString(chatDecoInfo.textColor, out txtColor))
        {
            lblChatContent.color = txtColor;
        }
        else
        {
            Debug.LogError("无法解析字体颜色: " + chatDecoInfo.textColor);
        }
        var chatBox = chatItem.GetChild("chatBox").asLoader;
        var resName = chatDecoInfo.resName;
        chatBox.url = PathUtil.GetChatDecoUrl(resName);
        chatBox.image.graphics.flip = fromWho == 0 ? FlipType.Horizontal: FlipType.None;

        contentNodeF.cell.height = chatItem.height;

        var reputationTitle = chatItemInfo.GetChild("reputationTitle").asLoader;
        if (vipLevel > 0)
        {
            reputationTitle.visible = true;
            reputationTitle.url = PathUtil.GetItemIcon("image_title_" + vipLevel);
        }
        else
        {
            reputationTitle.visible = false;
        }

        listChatContentF.scrollPane.ScrollBottom();
    }

    private GTreeNode CreateTimeNodeF(string timeStr)
    {
        GTreeNode timeNodeF = new GTreeNode(true, GetCurPackRes("ChatTimeItemF"));
        rootNodeF.AddChild(timeNodeF);
        timeNodeF.expanded = true;

        var lblState = timeNodeF.cell.GetChild("lblState").asTextField;
        lblState.text = timeStr;

        return timeNodeF;
    }

    private void OnFriendClick(long uid)
    {
        nowFriendUid = uid;
        if (friendInfoList.ContainsKey(uid))
        {
            OnUpdateChatFirendContent(friendInfoList.GetValueByKey(uid));
        }
        UpdateDataHint();
    }

    private void OnFriendCloseClick(long uid)
    {
        if (friendChatList.ContainsKey(uid))
        {
            NetMgr.C2SRequestDelFriendChat((int)uid);
            friendChatList.Remove(uid);
            friendInfoList.Remove(uid);
            for (int i = 0; i < talkedFriendList.Count; i++)
            {
                if (talkedFriendList[i].PlayerUid == uid)
                {
                    talkedFriendList.RemoveAt(i);
                    break;
                }
            }
            UpdateFriendChat();
        }

        UpdateDataHint();
    }

    private void ParseChatUpdateInfo(NetChatMsgRecord updateList)
    {
        UpdateChat(updateList);

        UpdateDataHint();
    }

    private void ParseChatListInfo(RepeatedField<NetChatMsgRecord> list)
    {
        for (int i = list.Count; i > 0; i--)
        {
            var item = list[i - 1];
            UpdateChat(item);
        }
    }

    private void OnClickSticker(GObject obj)
    {
        switch (activeChatIndex)
        {
            case 0:
                if (!FunStatusUtil.GetInstance().GetFunStatus(FunType.ChatWorld, true))
                {
                    return;
                }
                if (DateUtil.Now_s - lastWorldChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastWorldChatTime) + "s"));
                    return;
                }
                lastWorldChatTime = DateUtil.Now_s;
                break;
            case 1:
                if (!FunStatusUtil.GetInstance().GetFunStatus(FunType.ChatArea, true))
                {
                    return;
                }
                if (DateUtil.Now_s - lastTheaterChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastTheaterChatTime) + "s"));
                    return;
                }
                lastTheaterChatTime = DateUtil.Now_s;
                break;
            case 2:
                if (DateUtil.Now_s - lastGuildChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastGuildChatTime) + "s"));
                    return;
                }
                lastGuildChatTime = DateUtil.Now_s;
                break;
        }

        GObject clickedObject = obj;
        if (clickedObject == null)
        {
            return;
        }

        string stickerName = clickedObject.name;

        if (activeChatIndex == 3)
        {
            NetMgr.C2SRequestSendChatMsg(activeChatIndex + 1, 3, stickerName, (int)nowFriendUid);
            if (friendInfoList.ContainsKey(nowFriendUid))
            {
                var friendInfo = friendInfoList.GetValueByKey(nowFriendUid);
                bool isNew = true;
                for (int i = 0; i < talkedFriendList.Count; i++)
                {
                    if (talkedFriendList[i].PlayerUid == friendInfo.PlayerUid)
                    {
                        isNew = false;
                        break;
                    }
                }
                if (isNew)
                {
                    talkedFriendList.Add(friendInfo);
                }
            }
        }
        else
        {
            NetMgr.C2SRequestSendChatMsg(activeChatIndex + 1, 3, stickerName, 0);
        }

        boxSticker.visible = false;
    }

    private void OnClickStickerGroup(EventContext context)
    {
        activeStickerGroupIndex = listStickerGroup.selectedIndex;
        if (activeStickerGroupIndex == 0)
        {
            listEmoji.visible = true;
            listSticker.visible = false;
        }
        else
        {
            listEmoji.visible = false;
            listSticker.visible = true;

            int groupID = stickerGroupIds[activeStickerGroupIndex];
            if (stickerConfigDict.ContainsKey(groupID))
            {
                var stickerGroup = stickerConfigDict.GetValueByKey(groupID);
                listSticker.itemRenderer = OnRenderStickerItem;
                listSticker.data = stickerGroup;
                listSticker.numItems = stickerGroup.Count;
            }
        }
    }

    private void OnRenderStickerItem(int index, GObject obj)
    {
        var sticker = (listSticker.data as List<InfoSticker>)[index];
        var icon = obj.asCom.GetChild("icon").asLoader;
        icon.url = PathUtil.GetStickerUrl(sticker.effectID);
        obj.name = sticker.name;
        obj.onClick.Clear();
        obj.onClick.Add(() => OnClickSticker(obj));
    }

    private void OnClickTabItem(EventContext context)
    {
        if (listTab.selectedIndex == 2 && Session.playerLobbyInfo.GuildId <= 0)
        {
            if (listTab.selectedIndex == 2)
            {
                TipMgr.ShowErrorCodeTip(3108); // 聊天玩家还没加入公会
            }
            listTab.selectedIndex = activeChatIndex;
            return;
        }
        activeChatIndex = listTab.selectedIndex;

        bool isFriendChat = activeChatIndex == 3;

        listChatContent.visible = !isFriendChat;
        listChatContentF.visible = isFriendChat;
        listChatFriends.visible = isFriendChat;

        if (isFriendChat)
        {
            UpdateFriendChat();
        }
        else
        {
            UpdateChatUI();
        }

        UpdateDataHint();
    }

    private void UpdateChat(NetChatMsgRecord info)
    {
        var type = info.ChannelType;
        switch (type)
        {
            case 1:
                ParseChatInfo(info, worldChatList);
                break;
            case 2:
                ParseChatInfo(info, theaterChatList);
                break;
            case 3:
                ParseChatInfo(info, guildChatList);
                break;
            case 4:
                break;
            default:
                Debug.LogWarning("未知的聊天类型: " + type);
                return;
        }

        UpdateChatUI();
    }

    private void ParseChatInfo(NetChatMsgRecord info, DictList<string, List<NetChatMsgRecord>> chatList)
    {
        var time = info.Time;
        var timeStr = GetTimeStr(time);
        if (!chatList.ContainsKey(timeStr))
        {
            chatList.Add(timeStr, new List<NetChatMsgRecord>());
        }

        var list = chatList.GetValueByKey(timeStr);
        list.Add(info);
    }

    private string GetTimeStr(long time)
    {
        long diffSeconds = DateUtil.Now_s - time;

        if (diffSeconds < 60)
        {
            return LangUtil.GetText("txtTimeTipMinute", 1) + LangUtil.GetText("txtTimeAgo");
        }

        double totalHours = diffSeconds / 3600.0;

        if (totalHours < 1)
        {
            int minutes = (int)Math.Floor(diffSeconds / 60.0);
            return LangUtil.GetText("txtTimeTipMinute", minutes) + LangUtil.GetText("txtTimeAgo");
        }
        else if (totalHours < 24)
        {
            int hours = (int)Math.Floor(totalHours);
            return LangUtil.GetText("txtTimeTipHour", hours) + LangUtil.GetText("txtTimeAgo");
        }
        else
        {
            int days = (int)Math.Floor(totalHours / 24);
            return LangUtil.GetText("txtTimeTipDay", days) + LangUtil.GetText("txtTimeAgo");
        }
    }

    private void UpdateChatUI()
    {
        rootNode.RemoveChildren();

        DictList<string, List<NetChatMsgRecord>> chatList = null;
        switch (activeChatIndex)
        {
            case 0:
                chatList = worldChatList;
                break;
            case 1:
                chatList = theaterChatList;
                break;
            case 2:
                chatList = guildChatList;
                break;
            case 3:
                break;
            default:
                Debug.LogWarning("未知的聊天索引: " + activeChatIndex);
                return;
        }

        var boxNotData = contentPane.GetChild("boxNotData").asGroup;
        if (chatList == null || chatList.Count == 0)
        {
            boxNotData.visible = true;
            return;
        }

        boxNotData.visible = false;
        for (int i = 0; i < chatList.Count; i++)
        {
            var key = chatList.GetKeyByIndex(i);
            var list = chatList.GetValueByKey(key);

            var timeNode = CreateTimeNode(key);
            foreach (var info in list)
            {
                var playerName = info.FromPlayerInfo.Name;
                var theaterRank = info.FromPlayerInfo.AreaId.ToString();
                var vipLevel = (int)info.FromPlayerInfo.VipLevel;
                var iconID = info.FromPlayerInfo.Icon;
                var iconFrameID = info.FromPlayerInfo.IconFrame;
                var decoID = info.FromPlayerInfo.ChatBg;

                int fromWho;
                if (info.FromPlayerInfo.PlayerUid == Session.playerLobbyInfo.PlayerUid)
                {
                    fromWho = 0;
                }
                else
                {
                    fromWho = 1;
                }

                if (info.MsgType == 3)
                {
                    var stickerName = info.ChatMsg;
                    CreateStickerNode(timeNode, playerName, stickerName, theaterRank, iconID, iconFrameID, decoID, fromWho, vipLevel);
                }
                else
                {
                    var content = info.ChatMsg;
                    CreateContentNode(timeNode, playerName, content, theaterRank, iconID, iconFrameID, decoID, fromWho, vipLevel);
                }
            }
        }

        UpdateDataHint();
    }

    private GTreeNode CreateTimeNode(string timeStr)
    {
        GTreeNode timeNode = new GTreeNode(true, GetCurPackRes("ChatTimeItem"));
        rootNode.AddChild(timeNode);
        timeNode.expanded = true;

        var lblState = timeNode.cell.GetChild("lblState").asTextField;
        lblState.text = timeStr;

        return timeNode;
    }


    private void CreateContentNode(GTreeNode parentNode, string playerName, string content, string theaterRank, string iconID, int iconFrameID, string decoID, int fromWho, int vipLevel) // fromWho 0: me, 1: other
    {
        GTreeNode contentNode = new GTreeNode(false, GetCurPackRes("ChatContentItem"));
        parentNode.AddChild(contentNode);

        var c1 = contentNode.cell.GetController("c1");
        c1.selectedIndex = fromWho;

        GComponent chatItem;
        if (fromWho == 0)
        {
            chatItem = contentNode.cell.GetChild("chatMyItem").asCom;
        }
        else
        {
            chatItem = contentNode.cell.GetChild("chatOtherItem").asCom;
        }

        var chatItemInfo = chatItem.GetChild("info").asCom;
        var lblPlayerName = chatItemInfo.GetChild("lblPlayerName").asTextField;
        var lblRankGroup = chatItemInfo.GetChild("lblRankGroup").asTextField;
        var lblChatContent = chatItem.GetChild("lblChatContent").asRichTextField;

        lblPlayerName.text = playerName;
        lblRankGroup.text = theaterRank;

        bool hasEmj = content.Contains("[bq");
        if (hasEmj)
        {
            System.Text.RegularExpressions.Regex regex = new System.Text.RegularExpressions.Regex(@"\[bq(\d{2})\]");

            string replacedContent = regex.Replace(content, match =>
            {
                string emojiNumber = match.Groups[1].Value;

                string emojiName = "[bq" + emojiNumber + "]";

                string effectID = ConfigSticker.GetEffectIDByName(emojiName);

                if (string.IsNullOrEmpty(effectID))
                {
                    Debug.LogWarning($"找不到表情: {emojiName}");
                    return match.Value;
                }

                return $"<img src='{PathUtil.GetStickerUrl(effectID)}' width='40' height='40'/>";
            });

            lblChatContent.text = replacedContent;
        }
        else
        {
            lblChatContent.text = content;
        }

        var avatar = chatItemInfo.GetChild("avatar").asCom;
        var icon = avatar.GetChild("icon").asLoader;
        var iconFrame = avatar.GetChild("iconFrame").asLoader;
        if (iconID == "")
        {
            icon.url = PathUtil.GetHeadIcon(0.ToString());
        }
        else
        {
            icon.url = PathUtil.GetHeadIcon(iconID);
        }
        if (iconFrameID == 0) // 空数据?
        {
            iconFrame.url = PathUtil.GetHeadFrame(3400);
        }
        else
        {
            iconFrame.url = PathUtil.GetHeadFrame(iconFrameID);
        }

        var chatDecoInfo = ConfigChatDeco.GetData(decoID);
        Color txtColor;
        if (ColorUtility.TryParseHtmlString(chatDecoInfo.textColor, out txtColor))
        {
            lblChatContent.color = txtColor;
        }
        else
        {
            Debug.LogError("无法解析字体颜色: " + chatDecoInfo.textColor);
        }
        var chatBox = chatItem.GetChild("chatBox").asLoader;
        var resName = chatDecoInfo.resName;
        chatBox.url = PathUtil.GetChatDecoUrl(resName);
        chatBox.image.graphics.flip = fromWho == 0 ? FlipType.Horizontal: FlipType.None;

        contentNode.cell.height = chatItem.height;

        var reputationTitle = chatItemInfo.GetChild("reputationTitle").asLoader;
        if (vipLevel > 0)
        {
            reputationTitle.visible = true;
            reputationTitle.url = PathUtil.GetItemIcon("image_title_" + vipLevel);
        }
        else
        {
            reputationTitle.visible = false;
        }

        listChatContent.scrollPane.ScrollBottom();
    }

    private void CreateStickerNode(GTreeNode parentNode, string playerName, string stickerName, string theaterRank, string iconID, int iconFrameID, string decoID, int fromWho, int vipLevel)
    {
        GTreeNode stickerNode = null;

        if (fromWho == 0)
        {
            stickerNode = new GTreeNode(false, GetCurPackRes("ChatMyItemSticker"));
        }
        else
        {
            stickerNode = new GTreeNode(false, GetCurPackRes("ChatOtherItemSticker"));
        }

        parentNode.AddChild(stickerNode);

        var info = stickerNode.cell.GetChild("info").asCom;
        var lblPlayerName = info.GetChild("lblPlayerName").asTextField;
        var lblRankGroup = info.GetChild("lblRankGroup").asTextField;

        lblPlayerName.text = playerName;
        lblRankGroup.text = theaterRank;

        var avatar = info.GetChild("avatar").asCom;
        var icon = avatar.GetChild("icon").asLoader;
        var iconFrame = avatar.GetChild("iconFrame").asLoader;
        if (iconID == "")
        {
            icon.url = PathUtil.GetHeadIcon(0.ToString());
        }
        else
        {
            icon.url = PathUtil.GetHeadIcon(iconID);
        }
        if (iconFrameID == 0) // 空数据?
        {
            iconFrame.url = PathUtil.GetHeadFrame(3400);
        }
        else
        {
            iconFrame.url = PathUtil.GetHeadFrame(iconFrameID);
        }

        var chatBox = stickerNode.cell.GetChild("chatBox").asLoader;
        var chatDecoInfo = ConfigChatDeco.GetData(decoID);
        var resName = chatDecoInfo.resName;
        chatBox.url = PathUtil.GetChatDecoUrl(resName);
        chatBox.image.graphics.flip = fromWho == 0 ? FlipType.Horizontal: FlipType.None;

        var effectSticker = stickerNode.cell.GetChild("effectSticker").asLoader;

        string effectID = ConfigSticker.GetEffectIDByName(stickerName);
        if (!string.IsNullOrEmpty(effectID))
        {
            effectSticker.url = PathUtil.GetStickerUrl(effectID);
        }
        else
        {
            Debug.LogError($"找不到贴纸: {stickerName}");
            effectSticker.url = "";
        }

        var reputationTitle = info.GetChild("reputationTitle").asLoader;
        if (vipLevel > 0)
        {
            reputationTitle.visible = true;
            reputationTitle.url = PathUtil.GetItemIcon("image_title_" + vipLevel);
        }
        else
        {
            reputationTitle.visible = false;
        }

        listChatContent.scrollPane.ScrollBottom();
    }

    private void OnSendBtnClick()
    {
        if (inputCode.text.Length <= 0)
        {
            return;
        }

        switch (activeChatIndex)
        {
            case 0:
                // 功能开放
                if (!FunStatusUtil.GetInstance().GetFunStatus(FunType.ChatWorld, true))
                {
                    return;
                }
                if (DateUtil.Now_s - lastWorldChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastWorldChatTime) + "s"));
                    return;
                }
                lastWorldChatTime = DateUtil.Now_s;
                break;
            case 1:
                if (!FunStatusUtil.GetInstance().GetFunStatus(FunType.ChatArea, true))
                {
                    return;
                }
                if (DateUtil.Now_s - lastTheaterChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastTheaterChatTime) + "s"));
                    return;
                }
                lastTheaterChatTime = DateUtil.Now_s;
                break;
            case 2:
                if (DateUtil.Now_s - lastGuildChatTime < ChatIntervals)
                {
                    TipMgr.ShowTip(LangUtil.GetText("txtChatCountdown", ChatIntervals - (DateUtil.Now_s - lastGuildChatTime) + "s"));
                    return;
                }
                lastGuildChatTime = DateUtil.Now_s;
                break;
        }

        if (activeChatIndex == 3)
        {
            NetMgr.C2SRequestSendChatMsg(activeChatIndex + 1, 0, inputCode.text, (int)nowFriendUid);
            if (friendInfoList.ContainsKey(nowFriendUid))
            {
                var friendInfo = friendInfoList.GetValueByKey(nowFriendUid);
                bool isNew = true;
                for (int i = 0; i < talkedFriendList.Count; i++)
                {
                    if (talkedFriendList[i].PlayerUid == friendInfo.PlayerUid)
                    {
                        isNew = false;
                        break;
                    }
                }
                if (isNew)
                {
                    talkedFriendList.Add(friendInfo);
                }
            }
        }
        else
        {
            NetMgr.C2SRequestSendChatMsg(activeChatIndex + 1, 0, inputCode.text, 0);
        }

        inputCode.text = "";
    }

    private void OnStickerBtnClick()
    {
        boxSticker.visible = !boxSticker.visible;
    }

    internal void OpenFromFriends(NetFriendInfo data)
    {
        listTab.selectedIndex = 3;
        activeChatIndex = listTab.selectedIndex;

        UpdateFriendInfoList(data);
        nowFriendUid = data.PlayerUid;

        if (!friendChatList.ContainsKey(data.PlayerUid))
        {
            friendChatList.Add(data.PlayerUid, new List<NetChatMsgRecord>());
        }

        for (int i = 0; i < talkedFriendList.Count; i++)
        {
            if (talkedFriendList[i].PlayerUid == data.PlayerUid)
            {
                talkedFriendList.RemoveAt(i);
                break;
            }
        }

        talkedFriendList.Add(data);

        UpdateFriendChat();
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnCancel":
                Hide();
                break;
            case "btnSend":
                OnSendBtnClick();
                break;
            case "btnSticker":
                OnStickerBtnClick();
                break;
        }
    }
}